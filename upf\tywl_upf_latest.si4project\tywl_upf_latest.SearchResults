---- id Matches (515 in 0 files) ----
{anonupf_downlink_buf_msg_t} in upf_pfcp_server.c (src\plugins\upf) :   u32 far_id;
upf_pfcp_server.c (src\plugins\upf) line 84 : static void upf_pfcp_response_make (sx_msg_t *resp, sx_msg_t *req, size_t len);
upf_pfcp_server.c (src\plugins\upf) line 85 : static void reset_response_timer (sx_msg_t *msg);
upf_pfcp_server.c (src\plugins\upf) line 91 : extern void upf_dnn_init2(pfcp_session_establishment_request_t *req, sx_msg_t *msg);
upf_pfcp_server.c (src\plugins\upf) line 93 : extern void upf_redis_reget_fail_list_timer();
upf_pfcp_server.c (src\plugins\upf) line 109 : static void *upf_push_ethernet_to_buffer (vlib_main_t * vm, vlib_buffer_t * b,
upf_pfcp_server.c (src\plugins\upf) line 127 : u16 upf_udp_checksum_ip4 (const void *b, u32 len, u8 * src, u8 * dst)
upf_pfcp_server.c (src\plugins\upf) line 165 : void
upf_send_pfcp_data in upf_pfcp_server.c (src\plugins\upf) :   hb->flags |= VLIB_BUFFER_TOTAL_LENGTH_VALID;
upf_send_pfcp_data in upf_pfcp_server.c (src\plugins\upf) :       const dpo_id_t *dpo = load_balance_get_bucket_i (load_balance_get (lb_index), 0);
upf_send_pfcp_data in upf_pfcp_server.c (src\plugins\upf) :         sess = upf_session_lookup (be64toh (msg->hdr->session_hdr.seid));
upf_send_pfcp_data in upf_pfcp_server.c (src\plugins\upf) :           memcpy(&msg->user_id, &sess->user_id, sizeof(msg->user_id));
upf_send_pfcp_data in upf_pfcp_server.c (src\plugins\upf) :           msg->user_id.nai = NULL;
sx_session_msg_encode in upf_pfcp_server.c (src\plugins\upf) :   msg->session_index = sx->up_seid;
sx_session_msg_encode in upf_pfcp_server.c (src\plugins\upf) :   msg->hdr->session_hdr.seid = clib_host_to_net_u64 (sx->cp_seid);
sx_session_msg_encode in upf_pfcp_server.c (src\plugins\upf) :   memcpy(&msg->user_id, &sx->user_id, sizeof(msg->user_id));
sx_session_msg_encode in upf_pfcp_server.c (src\plugins\upf) :   msg->user_id.nai = NULL;
upf_pfcp_server_msg_rx in upf_pfcp_server.c (src\plugins\upf) :       upf_err ("PFCP: msg version invalid: %d.", msg->hdr->version);
upf_pfcp_server_msg_rx in upf_pfcp_server.c (src\plugins\upf) :       upf_err ("PFCP: msg length invalid, data %d, msg %d.", len,
upf_pfcp_server_msg_rx in upf_pfcp_server.c (src\plugins\upf) :                 upf_info ("Response found, resend... msg idx %d SN:%d\n", kv.value,
upf_pfcp_server_msg_rx in upf_pfcp_server.c (src\plugins\upf) :             upf_debug ("Msg Seq No: %u, request msg_pool idx %u\n", msg->seq_no, kv.value);
sx_session_msg_build in upf_pfcp_server.c (src\plugins\upf) :   CHECK_POOL_IS_VALID_RET(sxsm->msg_pool, msg_index, NULL);
sx_session_msg_build in upf_pfcp_server.c (src\plugins\upf) :   vec_add1 (sx->msgs_id, msg - sxsm->msg_pool);
sx_node_msg_build in upf_pfcp_server.c (src\plugins\upf) :   CHECK_POOL_IS_VALID_RET(sxsm->msg_pool, msg_index, NULL);
upf_pfcp_server.c (src\plugins\upf) line 694 : static void
sx_enqueue_request in upf_pfcp_server.c (src\plugins\upf) :   u32 id = msg - sxsm->msg_pool;
sx_enqueue_request in upf_pfcp_server.c (src\plugins\upf) :   upf_debug ("Msg Seq No: %u, idx %u\n", msg->seq_no, id);
sx_enqueue_request in upf_pfcp_server.c (src\plugins\upf) :   kv.value = id;
sx_enqueue_request in upf_pfcp_server.c (src\plugins\upf) :   upf_pfcp_server_timer_start (PFCP_SERVER_T1, id, &msg->timer);
upf_pfcp_server.c (src\plugins\upf) line 731 : static void
upf_pfcp_server.c (src\plugins\upf) line 732 : request_time_expired (u32 id)
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :   CHECK_POOL_IS_VALID_NORET(sxsm->msg_pool, id);
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :   sx_msg_t *msg = pool_elt_at_index (sxsm->msg_pool, id);
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :   upf_debug ("Msg Seq No: %u, %p, idx %u, n1 %u\n", msg->seq_no, msg, id,
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :       upf_pfcp_server_timer_start (PFCP_SERVER_T1, id, &msg->timer);
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :           upf_err ("The CP node(%U) unreachable, release the Association.", upf_format_node_id, &n->node_id);
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :           uword *p = hash_get_mem (gtm->asso_alarm_index, &n->node_id.ip);
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :               asso_alarm_status->remote_ip = n->node_id.ip;
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :               upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(pfcp_node_id_t), CLIB_CACHE_LINE_BYTES);
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :               memset (upf_alarm->data, 0, sizeof (pfcp_node_id_t));
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :               upf_alarm->alarm_id = UPF_ALARM_ASSOCIATION_TIMEOUT;
request_time_expired in upf_pfcp_server.c (src\plugins\upf) :               memcpy(upf_alarm->data, &n->node_id, sizeof(pfcp_node_id_t));
upf_pfcp_server.c (src\plugins\upf) line 823 : void upf_gtp_echo_req (upf_peer_t *peer0, u32 peer_idx)
upf_gtp_echo_req in upf_pfcp_server.c (src\plugins\upf) :     upf_gtpu_send_echo_req (peer_idx);
upf_pfcp_server.c (src\plugins\upf) line 831 : static void
upf_pfcp_server.c (src\plugins\upf) line 832 : gtpu_echo_req_time_expired (u32 id)
gtpu_echo_req_time_expired in upf_pfcp_server.c (src\plugins\upf) :   if (pool_is_free_index (gtm->peers, id))
gtpu_echo_req_time_expired in upf_pfcp_server.c (src\plugins\upf) :   peer0 = pool_elt_at_index (gtm->peers, id);
gtpu_echo_req_time_expired in upf_pfcp_server.c (src\plugins\upf) :       upf_debug ("resend echo req idx:%u seq:%u n1 %u remote ip:%U\n", id,
gtpu_echo_req_time_expired in upf_pfcp_server.c (src\plugins\upf) :       upf_pfcp_server_timer_start (GTPU_ECHO_REQ_T1, id, &peer0->retry_timer);
gtpu_echo_req_time_expired in upf_pfcp_server.c (src\plugins\upf) :       upf_gtp_echo_req (peer0, id);
gtpu_echo_req_time_expired in upf_pfcp_server.c (src\plugins\upf) :       upf_debug ("GTPu echo req fail, send node report req! peer_index is %u, Remote IP:%U\n", id,
gtpu_echo_req_time_expired in upf_pfcp_server.c (src\plugins\upf) :         upf_alarm->alarm_id = UPF_ALARM_GTP_TUNNEL_TIMEOUT;
upf_pfcp_server.c (src\plugins\upf) line 893 : static void
upf_pfcp_server.c (src\plugins\upf) line 901 : void
upf_pfcp_server_session_request_send in upf_pfcp_server.c (src\plugins\upf) :       if (sx->user_id.flags & USER_ID_IMSI)
upf_pfcp_server_session_request_send in upf_pfcp_server.c (src\plugins\upf) :         upf_log_ex ("imsi: %s code:%U\n", sx->user_id.imsi_str, format_hex,
upf_pfcp_server.c (src\plugins\upf) line 924 : static void
upf_pfcp_server.c (src\plugins\upf) line 937 : static void
upf_pfcp_server.c (src\plugins\upf) line 938 : response_time_expired (u32 id)
response_time_expired in upf_pfcp_server.c (src\plugins\upf) :   CHECK_POOL_IS_VALID_NORET(sxsm->msg_pool, id);
response_time_expired in upf_pfcp_server.c (src\plugins\upf) :   sx_msg_t *msg = pool_elt_at_index (sxsm->msg_pool, id);
response_time_expired in upf_pfcp_server.c (src\plugins\upf) :   upf_debug ("PFCP Msg no VRF %d from %U:%d to %U:%d seq %u idx %u\n",
response_time_expired in upf_pfcp_server.c (src\plugins\upf) :              clib_net_to_host_u16 (msg->rmt.port), msg->seq_no, id);
upf_pfcp_server.c (src\plugins\upf) line 959 : static void
reset_response_timer in upf_pfcp_server.c (src\plugins\upf) :   u32 id = msg - sxsm->msg_pool;
reset_response_timer in upf_pfcp_server.c (src\plugins\upf) :   upf_debug ("Msg Seq No: %u, idx %u\n", msg->seq_no, id);
reset_response_timer in upf_pfcp_server.c (src\plugins\upf) :   upf_pfcp_server_timer_start (PFCP_SERVER_RESPONSE, id, &msg->timer);
upf_pfcp_server.c (src\plugins\upf) line 973 : static void
upf_pfcp_server.c (src\plugins\upf) line 977 :   u32 id = msg - sxsm->msg_pool;
upf_pfcp_server.c (src\plugins\upf) line 984 :              clib_net_to_host_u16 (msg->rmt.port), msg->seq_no, id);
upf_pfcp_server.c (src\plugins\upf) line 988 :   kv.value = id;
upf_pfcp_server.c (src\plugins\upf) line 993 :   upf_pfcp_server_timer_start (PFCP_SERVER_RESPONSE, id, &msg->timer);
upf_pfcp_server.c (src\plugins\upf) line 997 : static void
upf_pfcp_server.c (src\plugins\upf) line 1011 : upf_pfcp_response_send (sx_msg_t *req, u64 cp_seid, u8 type,
upf_pfcp_response_send in upf_pfcp_server.c (src\plugins\upf) :   CHECK_POOL_IS_VALID_RET(sxsm->msg_pool, msg_index, 0);
upf_pfcp_response_send in upf_pfcp_server.c (src\plugins\upf) :       resp->hdr->session_hdr.seid = clib_host_to_net_u64 (cp_seid);
upf_pfcp_response_send in upf_pfcp_server.c (src\plugins\upf) :   memcpy(&resp->user_id, &req->user_id, sizeof(resp->user_id));
upf_pfcp_response_send in upf_pfcp_server.c (src\plugins\upf) :   resp->user_id.nai = NULL;
upf_pfcp_server.c (src\plugins\upf) line 1113 : static void
upf_pfcp_session_report_linked_usage in upf_pfcp_server.c (src\plugins\upf) :   u32 *linked_urr_id = 0;
upf_pfcp_session_report_linked_usage in upf_pfcp_server.c (src\plugins\upf) :           "i=%u, j=%u, reporting_urr->id=%u, urr_with_linked_urr->id=%u.\n", i,
upf_pfcp_session_report_linked_usage in upf_pfcp_server.c (src\plugins\upf) :           j, reporting_urr->id, urr_with_linked_urr->id);
upf_pfcp_session_report_linked_usage in upf_pfcp_server.c (src\plugins\upf) :       vec_foreach (linked_urr_id, urr_with_linked_urr->linked_urr_ids)
upf_pfcp_session_report_linked_usage in upf_pfcp_server.c (src\plugins\upf) :         upf_debug ("*linked_urr_id=%u.\n", *linked_urr_id);
upf_pfcp_session_report_linked_usage in upf_pfcp_server.c (src\plugins\upf) :         if (reporting_urr->id != *linked_urr_id)
upf_pfcp_session_report_linked_usage in upf_pfcp_server.c (src\plugins\upf) :                             report, UPF_INVALID_PDR);
upf_pfcp_server.c (src\plugins\upf) line 1161 : /* To find those urrs who link this urr id. */
upf_pfcp_server.c (src\plugins\upf) line 1162 : u32 upf_pfcp_linked_urr_list_found (struct rules *active, u32 target_urr_id, upf_urr_t ***linked_list)
upf_pfcp_linked_urr_list_found in upf_pfcp_server.c (src\plugins\upf) :         u32 *linked_urr_id;
upf_pfcp_linked_urr_list_found in upf_pfcp_server.c (src\plugins\upf) :         vec_foreach (linked_urr_id, urr->linked_urr_ids)
upf_pfcp_linked_urr_list_found in upf_pfcp_server.c (src\plugins\upf) :             if (target_urr_id == *linked_urr_id)
upf_pfcp_server.c (src\plugins\upf) line 1210 : void upf_pfcp_linked_urr_usage_accumulated (upf_urr_t *urr, upf_urr_t **linked_list)
upf_pfcp_server.c (src\plugins\upf) line 1234 : static void
upf_pfcp_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :         if (upf_pfcp_linked_urr_list_found (active, urr->id, &linked_list))
upf_pfcp_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :         /* Andy added, record current reporting urr id */
upf_pfcp_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :     /* Andy added, record current urr id if with  LIUSA*/
upf_pfcp_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :         (0 != vec_len (urr->linked_urr_ids)))
upf_pfcp_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :         upf_debug ("urr id=%u with linked urr id.\n", urr->id);
upf_pfcp_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :               if (g_upf_urr_flow_statistics[k].is_used && g_upf_urr_flow_statistics[k].urr_id == urr->id)
upf_pfcp_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :                       g_upf_urr_flow_statistics[k].urr_id = urr->id;
upf_pfcp_server.c (src\plugins\upf) line 1409 : void
upf_pfcp_server.c (src\plugins\upf) line 1431 : u32 upf_pfcp_timer_reset(u32 cb_index, u8 timer_id, urr_time_t *t, u8 status)
upf_pfcp_timer_reset in upf_pfcp_server.c (src\plugins\upf) :   u32 value = (timer_id << 24) | (cb_index & 0xFFFFFF);
upf_pfcp_server.c (src\plugins\upf) line 1458 : void
upf_pfcp_session_urr_time_start_stop in upf_pfcp_server.c (src\plugins\upf) :       u64 up_seid = 0;
upf_pfcp_session_urr_time_start_stop in upf_pfcp_server.c (src\plugins\upf) :         up_seid = sx->up_seid;
upf_pfcp_session_urr_time_start_stop in upf_pfcp_server.c (src\plugins\upf) :       upf_trace ("upseid 0x%lx starting URR timer, now is %.3f, base is %.3f, "
upf_pfcp_session_urr_time_start_stop in upf_pfcp_server.c (src\plugins\upf) :                  up_seid, now, t->base, t->period, interval,
upf_pfcp_server.c (src\plugins\upf) line 1505 : void
upf_pfcp_server.c (src\plugins\upf) line 1534 : void upf_mac_address_removed_append(upf_report_mac_information_t *report_info, upf_mac_addr_detect_t *t)
upf_mac_address_removed_append in upf_pfcp_server.c (src\plugins\upf) :     report_info->pdr_idx = t->pdr_index;
upf_mac_address_removed_append in upf_pfcp_server.c (src\plugins\upf) :     addr->c_tag.mask = VLAN_MASK_VID;
upf_mac_address_removed_append in upf_pfcp_server.c (src\plugins\upf) :     addr->s_tag.mask = VLAN_MASK_VID;
upf_pfcp_server.c (src\plugins\upf) line 1564 : void upf_mac_address_removed_report(upf_session_t *sess, upf_report_mac_information_t *report_info)
upf_mac_address_removed_report in upf_pfcp_server.c (src\plugins\upf) :     msg->msg_id = PFCP_RPC_PUBLISH_REPORT_MAC_ADDRESS_REPORT;
upf_mac_address_removed_report in upf_pfcp_server.c (src\plugins\upf) :     report_info->sess_idx = sess - um->sessions;
upf_pfcp_server.c (src\plugins\upf) line 1585 : void upf_traffic_inactive_detect_report(upf_session_t *sx, upf_urr_t *urr)
upf_traffic_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :     msg->msg_id = PFCP_RPC_PUBLISH_TRAF_INACT_DETECT_REPORT;
upf_traffic_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :     p->sess_idx = sx - um->sessions;
upf_traffic_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :         p->pdr_id = urr->traf_inact_detect.pdr_id;
upf_traffic_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :         p->has_pdrid = 1;
upf_traffic_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :     p->urr_id = urr->id;
upf_pfcp_per_qos_monitor_timer_handle in upf_pfcp_server.c (src\plugins\upf) :                                            upf_monitor_send_timer_t *timer, f64 now, u32 sx_idx, u8 trigger_flag)
upf_pfcp_per_qos_monitor_timer_handle in upf_pfcp_server.c (src\plugins\upf) :         upf_pfcp_monitor_send_timer_reset(timer, now, sx_idx, TIMER_START | TIMER_ADVANCE);
upf_pfcp_per_qos_monitor_timer_handle in upf_pfcp_server.c (src\plugins\upf) :         upf_debug("per qos monitor measure failure report, sx_idx:%u, qfi:%u", sx_idx, qfi);
upf_pfcp_per_qos_monitor_timer_handle in upf_pfcp_server.c (src\plugins\upf) :         if (!upf_gtpu_send_qmp_dummy_gtp (qfi, sx_idx))
upf_pfcp_per_qos_monitor_timer_handle in upf_pfcp_server.c (src\plugins\upf) :             upf_debug("send dummy failed, sx_idx:%u, qfi:%u", sx_idx, qfi);
upf_pfcp_session_srr_timer in upf_pfcp_server.c (src\plugins\upf) :     u32 sx_idx = sx - g_upf_main.sessions;
upf_pfcp_session_srr_timer in upf_pfcp_server.c (src\plugins\upf) :     upf_debug ("###up_seid:0x%lx, now:%.3f\n", sx->up_seid, now);
upf_pfcp_session_srr_timer in upf_pfcp_server.c (src\plugins\upf) :                     r = upf_pfcp_per_qos_monitor_timer_handle (per_qos_flow_ctrl, flow_ctrl, &flow_ctrl->periodic_report, now, sx_idx, QMP_TRIG_PERIOD);
upf_pfcp_session_srr_timer in upf_pfcp_server.c (src\plugins\upf) :                     r = upf_pfcp_per_qos_monitor_timer_handle (per_qos_flow_ctrl, flow_ctrl, &flow_ctrl->default_report, now, sx_idx, QMP_TRIG_DEFAULT);
upf_pfcp_session_srr_timer in upf_pfcp_server.c (src\plugins\upf) :             SET_BIT (sx_report.grp.fields, SESSION_REPORT_SRR_ID);
upf_pfcp_session_srr_timer in upf_pfcp_server.c (src\plugins\upf) :             sx_report.srr_id = srr->id;
upf_pfcp_server.c (src\plugins\upf) line 1710 : static void
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :   upf_debug ("up_seid: 0x%lx, sx: %p, now: %.3f", sx->up_seid, sx, now);
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :   u32 pdr_index = UPF_INVALID_PDR;
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :     urr_debug ("URR: %p, Id: %u",urr->id);
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :           upf_pdr_t *pdr = upf_get_pdr_by_id (active, urr->traffic.pdr_id);
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :           pdr_index = pdr ? (pdr - active->pdr) : UPF_INVALID_PDR;
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :     if ((urr->update_flags & SX_URR_UPDATE_IDT) && (urr->idt.period != 0))
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :         if (urr_check_time (urr->idt, now))
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :             urr->idt.base += urr->idt.period;
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :                     urr->time_threshold.consumed += urr->idt.period;
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :                     urr->time_quota.consumed += urr->idt.period;
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :             upf_pfcp_session_urr_time_start_stop (si, &urr->idt, 1, 0);
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :             /* To avoid repeat reporting */
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :         /* Andy added, record current reporting urr id */
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :     /* Andy added, record current urr id if with  LIUSA*/
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :         (0 != vec_len (urr->linked_urr_ids)))
upf_pfcp_session_timer_of_urr in upf_pfcp_server.c (src\plugins\upf) :         upf_debug ("urr id=%u with linked urr id.\n", urr->id);
upf_pfcp_server.c (src\plugins\upf) line 2000 : void
upf_pfcp_server.c (src\plugins\upf) line 2011 : void
upf_pfcp_server.c (src\plugins\upf) line 2012 : upf_pfcp_server_timer_start (u8 type, u32 id, pfcp_time_t *timer)
upf_pfcp_server_timer_start in upf_pfcp_server.c (src\plugins\upf) :   ASSERT ((id & 0xff000000) == 0);
upf_pfcp_server_timer_start in upf_pfcp_server.c (src\plugins\upf) :   timer->handle = tw_timer_start_1t_3w_1024sl_ov (&per_pfcp->timer, (type << 24) | id, PFCP_URR_TIMER, interval);
upf_pfcp_server.c (src\plugins\upf) line 2031 : void
upf_pfcp_server.c (src\plugins\upf) line 2032 : upf_oam_server_timer_start (u8 type, u32 id, oam_time_t *timer)
upf_oam_server_timer_start in upf_pfcp_server.c (src\plugins\upf) :   ASSERT ((id & 0xff000000) == 0);
upf_oam_server_timer_start in upf_pfcp_server.c (src\plugins\upf) :   timer->handle = tw_timer_start_1t_3w_1024sl_ov (&per_oam->timer, (type << 24) | id, PFCP_URR_TIMER, interval);
upf_pfcp_server.c (src\plugins\upf) line 2051 : static int upf_handle_alarm_db_agming(BVT (clib_bihash_kv) * kvp, void *arg)
upf_handle_alarm_db_agming in upf_pfcp_server.c (src\plugins\upf) :             upf_alarm->alarm_id = UPF_ALARM_UL_UEIP_CHECK_FAIL;
upf_handle_alarm_db_agming in upf_pfcp_server.c (src\plugins\upf) :             upf_alarm->alarm_id = UPF_ALARM_DL_SESSION_CHECK_FAIL;
upf_pfcp_server.c (src\plugins\upf) line 2103 : void
upf_pfcp_server.c (src\plugins\upf) line 2118 : void upf_recv_echo_rsp (ip46_sd_t *ip46, icmp46_header_t *icmp, u8 is_ip4)
upf_recv_echo_rsp in upf_pfcp_server.c (src\plugins\upf) :         if (p->host_id == clib_net_to_host_u16(icmp46_echo->id))
upf_recv_echo_rsp in upf_pfcp_server.c (src\plugins\upf) :             upf_trace (" %U  host_id:%u rsp-id:%u", format_ip46_sd, ip46,
upf_recv_echo_rsp in upf_pfcp_server.c (src\plugins\upf) :                 p->host_id, clib_net_to_host_u16(icmp46_echo->id));
upf_pfcp_server.c (src\plugins\upf) line 2142 : void upf_input_next_obtain(vlib_buffer_t *b, u8 is_ip4, ip46_sd_t *ip46)
ip4_icmp_compute_checksum in upf_pfcp_server.c (src\plugins\upf) :     void *data_this_buffer;
ip4_icmp_compute_checksum in upf_pfcp_server.c (src\plugins\upf) :     data_this_buffer = (void *) ip0 + ip_header_length;
upf_pfcp_server.c (src\plugins\upf) line 2227 : static void upf_ip46_fix_len_and_csum (vlib_main_t * vm, int l4_offset, u16 data_len, vlib_buffer_t * b0, int is_ip6)
ip46_echo_fill_l3_header in upf_pfcp_server.c (src\plugins\upf) :         ip4->fragment_id = 0;
upf_send_echo_req in upf_pfcp_server.c (src\plugins\upf) :     u32 table_id = 0;
upf_send_echo_req in upf_pfcp_server.c (src\plugins\upf) :         fib_index = ip6_fib_index_from_table_id (table_id);
upf_send_echo_req in upf_pfcp_server.c (src\plugins\upf) :         fib_index = ip4_fib_index_from_table_id (table_id);
upf_send_echo_req in upf_pfcp_server.c (src\plugins\upf) :     icmp46_echo->id = clib_host_to_net_u16 (health->host_id);
upf_pfcp_server.c (src\plugins\upf) line 2372 : void upf_health_check(u32 is_gre4)
upf_health_check in upf_pfcp_server.c (src\plugins\upf) :         upf_debug ("invalid ret.\n");
upf_pfcp_server.c (src\plugins\upf) line 2447 : void
upf_cpu_usage_timer in upf_pfcp_server.c (src\plugins\upf) :             if (w->cpu_id > -1)
upf_cpu_usage_timer in upf_pfcp_server.c (src\plugins\upf) :                 g_upf_cpu_usage[j].thread_id = j;
upf_cpu_usage_timer in upf_pfcp_server.c (src\plugins\upf) :                 g_upf_cpu_usage[j].cpu_id = w->cpu_id;
upf_cpu_usage_timer in upf_pfcp_server.c (src\plugins\upf) :         upf_alarm->alarm_id = UPF_ALARM_CPU_OVERLOAD;
upf_cpu_usage_timer in upf_pfcp_server.c (src\plugins\upf) :         upf_alarm->alarm_id = UPF_ALARM_CPU_OVERLOAD;
upf_pfcp_server.c (src\plugins\upf) line 2553 : void upf_cpu_usage_init()
upf_pfcp_server.c (src\plugins\upf) line 2562 : void upf_alarm_init()
upf_alarm_init in upf_pfcp_server.c (src\plugins\upf) :     memset(&g_upf_alarm_id_switch, 1, sizeof(g_upf_alarm_id_switch));
upf_pfcp_server.c (src\plugins\upf) line 2598 : void start_first_thread_timer(u8 type, u32 id, pfcp_time_t *timer)
start_first_thread_timer in upf_pfcp_server.c (src\plugins\upf) :     ASSERT ((id & 0xff000000) == 0);
start_first_thread_timer in upf_pfcp_server.c (src\plugins\upf) :     timer->handle = tw_timer_start_1t_3w_1024sl_ov (&per_pfcp->timer, (type << 24) | id, PFCP_URR_TIMER, interval);
upf_pfcp_server.c (src\plugins\upf) line 2619 : void stop_first_thread_timer(u32 handle)
upf_pfcp_server.c (src\plugins\upf) line 2630 : void upf_pfcp_msg_success_ratio_alarm(u32 total_cnt, u32 success_cnt, u32 success_ratio, u32 alarm_id)
upf_pfcp_msg_success_ratio_alarm in upf_pfcp_server.c (src\plugins\upf) :     if (g_upf_alarm_state[alarm_id - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER &&
upf_pfcp_msg_success_ratio_alarm in upf_pfcp_server.c (src\plugins\upf) :         success_ratio < g_upf_alarm_threshold[alarm_id - UPF_ALARM_BASE])
upf_pfcp_msg_success_ratio_alarm in upf_pfcp_server.c (src\plugins\upf) :         upf_alarm->alarm_id = alarm_id;
upf_pfcp_msg_success_ratio_alarm in upf_pfcp_server.c (src\plugins\upf) :         g_upf_alarm_state[alarm_id - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
upf_pfcp_msg_success_ratio_alarm in upf_pfcp_server.c (src\plugins\upf) :     if (g_upf_alarm_state[alarm_id - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE &&
upf_pfcp_msg_success_ratio_alarm in upf_pfcp_server.c (src\plugins\upf) :         success_ratio > g_upf_alarm_threshold[alarm_id - UPF_ALARM_BASE])
upf_pfcp_msg_success_ratio_alarm in upf_pfcp_server.c (src\plugins\upf) :         upf_alarm->alarm_id = alarm_id;
upf_pfcp_msg_success_ratio_alarm in upf_pfcp_server.c (src\plugins\upf) :         g_upf_alarm_state[alarm_id - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
upf_pfcp_server.c (src\plugins\upf) line 2689 : void upf_pfcp_msg_success_ratio_handle()
upf_pfcp_server.c (src\plugins\upf) line 2758 : void
upf_pfcp_server.c (src\plugins\upf) line 2759 : upf_server_send_heartbeat (u32 node_idx)
upf_server_send_heartbeat in upf_pfcp_server.c (src\plugins\upf) :   if (pool_is_free_index (gtm->nodes, node_idx))
upf_server_send_heartbeat in upf_pfcp_server.c (src\plugins\upf) :   n = pool_elt_at_index (gtm->nodes, node_idx);
upf_pfcp_server.c (src\plugins\upf) line 2780 : void
upf_pfcp_server.c (src\plugins\upf) line 2781 : upf_server_send_gtpu_echo_req (u32 peer_idx)
upf_server_send_gtpu_echo_req in upf_pfcp_server.c (src\plugins\upf) :   if (pool_is_free_index (gtm->peers, peer_idx))
upf_server_send_gtpu_echo_req in upf_pfcp_server.c (src\plugins\upf) :     upf_debug ("peer_idx:%u is invalid\n", peer_idx);
upf_server_send_gtpu_echo_req in upf_pfcp_server.c (src\plugins\upf) :   peer0 = pool_elt_at_index (gtm->peers, peer_idx);
upf_server_send_gtpu_echo_req in upf_pfcp_server.c (src\plugins\upf) :   kv.value = peer_idx;
upf_server_send_gtpu_echo_req in upf_pfcp_server.c (src\plugins\upf) :   upf_pfcp_server_timer_start (GTPU_ECHO_REQ_T1, peer_idx,
upf_server_send_gtpu_echo_req in upf_pfcp_server.c (src\plugins\upf) :   upf_debug ("send echo req idx:%u seq:%u remote ip:%U\n", peer_idx,
upf_server_send_gtpu_echo_req in upf_pfcp_server.c (src\plugins\upf) :   upf_gtp_echo_req (peer0, peer_idx);
upf_server_send_gtpu_echo_req in upf_pfcp_server.c (src\plugins\upf) :   upf_pfcp_server_timer_start (GTPU_ECHO_REQ_TIMER, peer_idx, &peer0->timer);
upf_pfcp_server.c (src\plugins\upf) line 2877 : u32 upf_set_node_id_by_asso_idx (pfcp_node_id_t *node_id, upf_node_assoc_t *n )
upf_set_node_id_by_asso_idx in upf_pfcp_server.c (src\plugins\upf) :         node_id->type = NID_IPv4;
upf_set_node_id_by_asso_idx in upf_pfcp_server.c (src\plugins\upf) :         ip46_address_set_ip4 (&node_id->ip, &ep->key.addr.ip4);
upf_set_node_id_by_asso_idx in upf_pfcp_server.c (src\plugins\upf) :         node_id->type = NID_IPv6;
upf_set_node_id_by_asso_idx in upf_pfcp_server.c (src\plugins\upf) :         ip46_address_set_ip6 (&node_id->ip, &ep->key.addr.ip6);
upf_pfcp_server.c (src\plugins\upf) line 2896 : void upf_server_send_node_report_req (u64 type, u32 asso_idx, upf_peer_t *peer0)
upf_server_send_node_report_req in upf_pfcp_server.c (src\plugins\upf) :     upf_debug ("node index: %d type:%d remote ip: %U ", asso_idx, type, format_ip46_address, &peer0->ohc_ip, IP46_TYPE_IP4);
upf_server_send_node_report_req in upf_pfcp_server.c (src\plugins\upf) :     if (pool_is_free_index (gtm->nodes, asso_idx))
upf_server_send_node_report_req in upf_pfcp_server.c (src\plugins\upf) :     upf_node_assoc_t *n = pool_elt_at_index (gtm->nodes, asso_idx);
upf_server_send_node_report_req in upf_pfcp_server.c (src\plugins\upf) :     if (upf_set_node_id_by_asso_idx (&req.request.node_id, n))
upf_server_send_node_report_req in upf_pfcp_server.c (src\plugins\upf) :     SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_NODE_ID);
upf_pfcp_server.c (src\plugins\upf) line 2968 : void
upf_server_send_association_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :   SET_BIT (req.grp.fields, ASSOCIATION_SETUP_RESPONSE_NODE_ID);
upf_server_send_association_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :   CHECK_POOL_IS_VALID_NORET(gtm->pfcp_endpoints, p[0]);
upf_server_send_association_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :       req.request.node_id.type = NID_IPv4;
upf_server_send_association_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :       ip46_address_set_ip4 (&req.request.node_id.ip, &ep->key.addr.ip4);
upf_server_send_association_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :       req.request.node_id.type = NID_IPv6;
upf_server_send_association_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :       ip46_address_set_ip6 (&req.request.node_id.ip, &ep->key.addr.ip6);
upf_pfcp_server.c (src\plugins\upf) line 3034 : void
upf_pfcp_server.c (src\plugins\upf) line 3035 : upf_server_send_association_up_features_update_pfcp_thread (void *arg)
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :   u64 node_idx = (u64)arg;
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :   upf_info ("node index: %d", node_idx);
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :   if (pool_is_free_index (gtm->nodes, node_idx))
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :   n = pool_elt_at_index (gtm->nodes, node_idx);
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :   SET_BIT (req.grp.fields, ASSOCIATION_SETUP_RESPONSE_NODE_ID);
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :   CHECK_POOL_IS_VALID_NORET(gtm->pfcp_endpoints, p[0]);
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :       req.request.node_id.type = NID_IPv4;
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :       ip46_address_set_ip4 (&req.request.node_id.ip, &ep->key.addr.ip4);
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :       req.request.node_id.type = NID_IPv6;
upf_server_send_association_up_features_update_pfcp_thread in upf_pfcp_server.c (src\plugins\upf) :       ip46_address_set_ip6 (&req.request.node_id.ip, &ep->key.addr.ip6);
upf_asso_request_command_fn in upf_pfcp_server.c (src\plugins\upf) :   SET_BIT (req.grp.fields, ASSOCIATION_SETUP_REQUEST_NODE_ID);
upf_asso_request_command_fn in upf_pfcp_server.c (src\plugins\upf) :       req.request.node_id.type = NID_IPv4;
upf_asso_request_command_fn in upf_pfcp_server.c (src\plugins\upf) :       ip46_address_set_ip4 (&req.request.node_id.ip, &dp_ip.ip4);
upf_asso_request_command_fn in upf_pfcp_server.c (src\plugins\upf) :       req.request.node_id.type = NID_IPv6;
upf_asso_request_command_fn in upf_pfcp_server.c (src\plugins\upf) :       ip46_address_set_ip6 (&req.request.node_id.ip, &dp_ip.ip6);
upf_pfcp_server.c (src\plugins\upf) line 3169 :                   "<x.x.x.x>] vrf_value <table_idx>",
upf_asso_update_command_fn in upf_pfcp_server.c (src\plugins\upf) :           if (unformat (line_input, "node_idx %ld", &node_index))
upf_asso_update_command_fn in upf_pfcp_server.c (src\plugins\upf) :           vlib_cli_output (vm, "node idx is not found in all nodes!!");
upf_asso_update_command_fn in upf_pfcp_server.c (src\plugins\upf) :               clib_error_return (0, "node idx is not found in all nodes!!");
upf_asso_update_command_fn in upf_pfcp_server.c (src\plugins\upf) :                 (void *)para_p);
upf_pfcp_server.c (src\plugins\upf) line 3287 :     .short_help = "upf asso update [node_idx X] "
upf_status_set_command_fn in upf_pfcp_server.c (src\plugins\upf) :                   (void *)para_p);
upf_pfcp_server.c (src\plugins\upf) line 3371 : static void
upf_pfcp_server.c (src\plugins\upf) line 3419 : static void
upf_pfcp_server.c (src\plugins\upf) line 3466 : void
upf_pfcp_server.c (src\plugins\upf) line 3475 : upf_downlink_forward_request (upf_session_t *sess, u32 far_id)
upf_downlink_forward_request in upf_pfcp_server.c (src\plugins\upf) :   msg->session_index = sess->up_seid;
upf_downlink_forward_request in upf_pfcp_server.c (src\plugins\upf) :   msg->far_id = far_id;
upf_pfcp_server.c (src\plugins\upf) line 3493 : static void
upf_buffering_process in upf_pfcp_server.c (src\plugins\upf) :       far = upf_get_far_by_id (active, msg->far_id);
sx_process in upf_pfcp_server.c (src\plugins\upf) :       (void)vlib_process_wait_for_event_or_clock (vm, 10e-3);
sx_process in upf_pfcp_server.c (src\plugins\upf) :                     // upf_debug ("URR Event on Session Idx: %wd, %p\n", si,
sx_process in upf_pfcp_server.c (src\plugins\upf) :                     upf_pfcp_session_report_usage (sx, sxsm->now, UPF_INVALID_PDR);
upf_pfcp_server.c (src\plugins\upf) line 3642 : void dns_sniffer_ageing_timer()
dns_sniffer_ageing_timer in upf_pfcp_server.c (src\plugins\upf) :             //rte_hash_del_key(ip_h,(void *)(&g_rule[i].uip));
dns_sniffer_ageing_timer in upf_pfcp_server.c (src\plugins\upf) :             rte_hash_del_key(url_h,(void *)(&g_rule[i].acHost));
upf_pfcp_server.c (src\plugins\upf) line 3685 : void upf_dynmic_mac_state_refresh(vlib_main_t *vm)
upf_pfcp_server.c (src\plugins\upf) line 3715 : void upf_dynmic_mac_inactive_delete(vlib_main_t *vm)
upf_pfcp_server.c (src\plugins\upf) line 3758 : void
upf_pfcp_server.c (src\plugins\upf) line 3759 : upf_server_send_node_tunnel_report_req (u32 type, u32 node_idx,char *tunnel1)
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :   if (pool_is_free_index (gtm->nodes, node_idx))
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :   n = pool_elt_at_index (gtm->nodes, node_idx);
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :   SET_BIT (req.grp.fields, NODE_REPORT_REQUEST_NODE_ID);
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :   CHECK_POOL_IS_VALID_NORET(gtm->pfcp_endpoints, p[0]);
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :       req.request.node_id.type = NID_IPv4;
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :       ip46_address_set_ip4 (&req.request.node_id.ip, &ep->key.addr.ip4);
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :       req.request.node_id.type = NID_IPv6;
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :       ip46_address_set_ip6 (&req.request.node_id.ip, &ep->key.addr.ip6);
upf_server_send_node_tunnel_report_req in upf_pfcp_server.c (src\plugins\upf) :   req.vendor_specific_node_report.vendor_id = 33333;
upf_pfcp_server.c (src\plugins\upf) line 3857 : static void
upf_pfcp_server.c (src\plugins\upf) line 3872 : static void
upf_pfcp_server.c (src\plugins\upf) line 3888 : void add_gw_route(char *add_ip,char *del_ip)
upf_pfcp_server.c (src\plugins\upf) line 3902 : void *upf_gw_ipsec_backup_detect()
upf_pfcp_server.c (src\plugins\upf) line 4054 : void upf_gw_gre_backup_detect()
upf_pfcp_server.c (src\plugins\upf) line 4204 : void upf_single_gre_detect()
upf_pfcp_server.c (src\plugins\upf) line 4297 : void upf_single_ipsec_detect()
upf_pfcp_server.c (src\plugins\upf) line 4377 : void upf_run_independent_timer_reset (u32 time)
upf_run_independent_timer_reset in upf_pfcp_server.c (src\plugins\upf) :   u32 id = UPF_RUN_INDEPENDENT_TIMER;
upf_run_independent_timer_reset in upf_pfcp_server.c (src\plugins\upf) :   upf_pfcp_server_timer_start (id, id, &um->run_independent_timer);
upf_pfcp_server.c (src\plugins\upf) line 4394 : void upf_run_independent_alarm_report (alarm_status_e state)
upf_run_independent_alarm_report in upf_pfcp_server.c (src\plugins\upf) :   upf_alarm->alarm_id = UPF_ALARM_RUN_INDEPENDENT;
upf_pfcp_server.c (src\plugins\upf) line 4533 : void upf_s_nssai_add_del (upf_s_nssai_t *t, u32 is_add)
upf_pfcp_server.c (src\plugins\upf) line 4548 : always_inline void upf_time_delay_record (upf_time_delay_t *t, u32 delay)
upf_pfcp_server.c (src\plugins\upf) line 4556 : void upf_psa_delay_per_s_nssai_record (upf_psa_delay_per_snssai_t *n, pfcp_qos_monitoring_measurement_t *measure)
upf_pfcp_server.c (src\plugins\upf) line 4563 : always_inline void upf_time_delay_calc (upf_time_delay_t *n)
upf_pfcp_server.c (src\plugins\upf) line 4577 : void upf_psa_delay_per_s_nssai_update (void)
upf_pfcp_server.c (src\plugins\upf) line 4590 : void upf_psa_delay_timer_init (void)
upf_pfcp_server.c (src\plugins\upf) line 4598 : void upf_psa_delay_timer_handle (void)
upf_pfcp_server.c (src\plugins\upf) line 4690 : void upf_statistics_timer_init(void)
upf_pfcp_server.c (src\plugins\upf) line 4699 : void upf_global_statistics_timer_init(void)
upf_pfcp_server.c (src\plugins\upf) line 4708 : void upf_status_report_timer_init(void)
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :   u32 pool_index, timer_id;
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :       /* Get session index and timer id */
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :       timer_id = expired[i] >> 24;
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :       switch (timer_id)
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :               upf_far_t *far = upf_get_far_by_id (active, pdr->far_id);
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :               if (far->bar_id != ~0)
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :             clib_bihash_foreach_key_value_pair_8_8(&g_upf_main.session_by_id, upf_frer_dd_aging_timer_handle, NULL);
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :             upf_pkt_rate_timer_handle(timer_id);
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :           upf_debug ("timeout for unknown id: %u", expired[i] >> 24);
upf_pfcp_server.c (src\plugins\upf) line 4946 : void upf_pfcp_ip_multicast_info_report(upf_report_ip_multicast_information_t *mip_info)
upf_pfcp_ip_multicast_info_report in upf_pfcp_server.c (src\plugins\upf) :     u32 *urr_id;
upf_pfcp_ip_multicast_info_report in upf_pfcp_server.c (src\plugins\upf) :     CHECK_POOL_IS_VALID_NORET(gtm->sessions, mip_info->sess_idx);
upf_pfcp_ip_multicast_info_report in upf_pfcp_server.c (src\plugins\upf) :     upf_session_t *sx = pool_elt_at_index (gtm->sessions, mip_info->sess_idx);
upf_pfcp_ip_multicast_info_report in upf_pfcp_server.c (src\plugins\upf) :     upf_pdr_t *pdr = active->pdr + mip_info->pdr_idx;
upf_pfcp_ip_multicast_info_report in upf_pfcp_server.c (src\plugins\upf) :         upf_err("Can not found pdr:%u", mip_info->pdr_idx);
upf_pfcp_ip_multicast_info_report in upf_pfcp_server.c (src\plugins\upf) :     vec_foreach (urr_id, pdr->urr_ids)
upf_pfcp_ip_multicast_info_report in upf_pfcp_server.c (src\plugins\upf) :       urr = upf_get_urr_by_id (active, *urr_id);
upf_pfcp_ip_multicast_info_report in upf_pfcp_server.c (src\plugins\upf) :           upf_err("Can not found urr:%u", *urr_id);
upf_pfcp_server.c (src\plugins\upf) line 5027 : void upf_multicast_address_report(upf_multicast_addr_t *ip_m, u32 session_idx, u32 pdr_idx)
upf_multicast_address_report in upf_pfcp_server.c (src\plugins\upf) :     report.pdr_idx = pdr_idx;
upf_multicast_address_report in upf_pfcp_server.c (src\plugins\upf) :     report.sess_idx = session_idx;
upf_multicast_address_report in upf_pfcp_server.c (src\plugins\upf) :     msg->msg_id = PFCP_RPC_PUBLISH_REPORT_IP_MULTICAST;
upf_pfcp_server.c (src\plugins\upf) line 5088 : void upf_pfcp_mac_address_info_report(upf_report_mac_information_t *eth_info)
upf_pfcp_mac_address_info_report in upf_pfcp_server.c (src\plugins\upf) :     u32 *urr_id;
upf_pfcp_mac_address_info_report in upf_pfcp_server.c (src\plugins\upf) :     CHECK_POOL_IS_VALID_NORET(gtm->sessions, eth_info->sess_idx);
upf_pfcp_mac_address_info_report in upf_pfcp_server.c (src\plugins\upf) :     upf_session_t *sx = pool_elt_at_index (gtm->sessions, eth_info->sess_idx);
upf_pfcp_mac_address_info_report in upf_pfcp_server.c (src\plugins\upf) :     upf_pdr_t *pdr = active->pdr + eth_info->pdr_idx;
upf_pfcp_mac_address_info_report in upf_pfcp_server.c (src\plugins\upf) :         upf_err("Can not found pdr:%u", eth_info->pdr_idx);
upf_pfcp_mac_address_info_report in upf_pfcp_server.c (src\plugins\upf) :     vec_foreach (urr_id, pdr->urr_ids)
upf_pfcp_mac_address_info_report in upf_pfcp_server.c (src\plugins\upf) :         urr = upf_get_urr_by_id (active, *urr_id);
upf_pfcp_mac_address_info_report in upf_pfcp_server.c (src\plugins\upf) :             upf_err("Can not found urr:%u", *urr_id);
upf_pfcp_server.c (src\plugins\upf) line 5164 : void upf_pfcp_traf_inactive_detect_report(upf_inner_report_info_t *info)
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :     upf_session_t *sx = sx_get_by_index(info->sess_idx);
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :         upf_debug("Can not found sess_idx:%u", info->sess_idx);
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :     if (info->has_pdrid)
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :         pdr = upf_get_pdr_by_id (active, info->pdr_id);
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :             upf_debug("Can not found pdr:%u", info->pdr_id);
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :     req.report_type = REPORT_TYPE_TIDR;
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :     if (info->has_pdrid)
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :         req.traffic_inactive_report.pdr_id = info->pdr_id;
upf_pfcp_traf_inactive_detect_report in upf_pfcp_server.c (src\plugins\upf) :     req.traffic_inactive_report.urr_id = info->urr_id;
upf_pfcp_server.c (src\plugins\upf) line 5205 : void upf_pfcp_per_qos_flow_qos_monitor_report(upf_inner_report_info_t *info)
upf_pfcp_per_qos_flow_qos_monitor_report in upf_pfcp_server.c (src\plugins\upf) :     upf_session_t *sx = sx_get_by_index(info->sess_idx);
upf_pfcp_per_qos_flow_qos_monitor_report in upf_pfcp_server.c (src\plugins\upf) :         upf_debug("Can not found sess_idx:%u", info->sess_idx);
upf_pfcp_per_qos_flow_qos_monitor_report in upf_pfcp_server.c (src\plugins\upf) :     if (!info->has_srrid)
upf_pfcp_per_qos_flow_qos_monitor_report in upf_pfcp_server.c (src\plugins\upf) :     upf_srr_t *srr = upf_get_srr_by_id (active, info->srr_id);
upf_pfcp_per_qos_flow_qos_monitor_report in upf_pfcp_server.c (src\plugins\upf) :         upf_debug("Can not found srr:%u", info->srr_id);
upf_pfcp_per_qos_flow_qos_monitor_report in upf_pfcp_server.c (src\plugins\upf) :     SET_BIT (sx_report.grp.fields, SESSION_REPORT_SRR_ID);
upf_pfcp_per_qos_flow_qos_monitor_report in upf_pfcp_server.c (src\plugins\upf) :     sx_report.srr_id = info->srr_id;
upf_pfcp_server.c (src\plugins\upf) line 5241 : void upf_pfcp_input_handle (vlib_main_t *vm, vlib_buffer_t *b, int is_ip4)
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :   u64 cp_seid = 0;
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :       cp_seid = 0;
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :   else if (0 == msg->hdr->session_hdr.seid &&
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :       cp_seid = m.session_establishment_request.f_seid.seid;
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :       memcpy(&msg->user_id, &m.session_establishment_request.user_id, sizeof(msg->user_id));
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :       msg->user_id.nai = NULL;
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :       if (!(sess = upf_session_lookup (be64toh (msg->hdr->session_hdr.seid))))
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :                     be64toh (msg->hdr->session_hdr.seid));
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :           cp_seid = sess->cp_seid;
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :           if (sess->user_id.flags & USER_ID_IMSI)
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :               upf_log_ex ("imsi: %s msg_type: %d \n", sess->user_id.imsi_str,
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :                             sess->user_id.imsi_str);
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :                   upf_info ("PFCP: pre-Decode fail! message type: %d cause=%d, up_seid:0x%lx",
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :                             msg->hdr->type, r, msg->hdr->session_hdr.seid);
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :                   "imsi: %s code:\n%U\n", sess->user_id.imsi_str, format_hex,
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :       upf_session_t *sess = upf_session_lookup (be64toh (msg->hdr->session_hdr.seid));
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :         memcpy (&msg->user_id, &sess->user_id, sizeof(msg->user_id));
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :         msg->user_id.nai = NULL;
upf_pfcp_input_handle in upf_pfcp_server.c (src\plugins\upf) :   u32 thread_index = cp_seid % gtm->num_pfcp_threads;
upf_pfcp_server.c (src\plugins\upf) line 5465 : void
upf_pfcp_server.c (src\plugins\upf) line 5466 : upf_pfcp_server_session_report_usage (upf_session_t *sess, u32 pdr_idx)
upf_pfcp_server_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :   /* neil.fan@20220803 add this parameter to support APP ID for urr report refers to 29244 5.4.11,
upf_pfcp_server_session_report_usage in upf_pfcp_server.c (src\plugins\upf) :   event->opaque = pdr_idx;
upf_pfcp_server.c (src\plugins\upf) line 5487 : void
upf_pfcp_server_event_rx in upf_pfcp_server.c (src\plugins\upf) :             // upf_debug ("URR Event on Session Idx: %wd, %p\n", si,
upf_pfcp_server_event_rx in upf_pfcp_server.c (src\plugins\upf) :         u32 sidx = event->si;
upf_pfcp_server_event_rx in upf_pfcp_server.c (src\plugins\upf) :         upf_pfcp_server_timer_start (PFCP_SERVER_DL_REPORT, sidx, &timer);
upf_pfcp_server_rx_alarm in upf_pfcp_server.c (src\plugins\upf) :   upf_alarm_notify(alarm->alarm_id, alarm->alarm_type, alarm->alarm_level, alarm->data);
upf_pfcp_server.c (src\plugins\upf) line 5585 : void upf_l2_unicast_hash_handle(void *data)
upf_l2_unicast_hash_handle in upf_pfcp_server.c (src\plugins\upf) :         if (l2_forw->port_index < UPF_L2_INVALID_IDX_START)
upf_l2_unicast_hash_handle in upf_pfcp_server.c (src\plugins\upf) :                 l2_forw->key.nwi_idx = sx->vn_nwi_index;
upf_pfcp_server.c (src\plugins\upf) line 5615 : void upf_clear_buff_handle(void *data)
upf_clear_buff_handle in upf_pfcp_server.c (src\plugins\upf) :     u32 far_id = msg_buf->far_id;
upf_clear_buff_handle in upf_pfcp_server.c (src\plugins\upf) :         upf_err("upf get buffer clear sess failed, sess_idx:%u\n", sess_index);
upf_clear_buff_handle in upf_pfcp_server.c (src\plugins\upf) :     far = upf_get_far_by_id (active, far_id);
upf_clear_buff_handle in upf_pfcp_server.c (src\plugins\upf) :         upf_err("upf get buffer clear far failed, far_id:%u\n", far_id);
upf_pfcp_server.c (src\plugins\upf) line 5651 : void upf_broadcast_hash_handle(void *data)
upf_broadcast_hash_handle in upf_pfcp_server.c (src\plugins\upf) :     upf_err("upf_broadcast_hash_handle invalid sess index!");
upf_pfcp_server_msg_handle in upf_pfcp_server.c (src\plugins\upf) :     switch (msg->msg_id)
upf_pfcp_server_msg_handle in upf_pfcp_server.c (src\plugins\upf) :             upf_pfcp_events_publish (msg->msg_id, NULL, msg->data);
upf_pfcp_thread_handle_msg in upf_pfcp_server.c (src\plugins\upf) :     switch (msg->msg_id)
upf_pfcp_server.c (src\plugins\upf) line 5806 :     .runtime_data_bytes = sizeof (void *),
---- upf_server_send_heartbeat Matches (2 in 1 files) ----
upf_pfcp_server.c (src\plugins\upf) line 2759 : upf_server_send_heartbeat (u32 node_idx)
upf_pfcp_timer_process in upf_pfcp_server.c (src\plugins\upf) :           upf_server_send_heartbeat (pool_index);
