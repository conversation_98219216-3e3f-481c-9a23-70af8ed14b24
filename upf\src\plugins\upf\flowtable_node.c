/*
 * Copyright (c) 2016 Qosmos and/or its affiliates.
 * Copyright (c) 2018 Travelping GmbH
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at:
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <vppinfra/dlist.h>
#include <vppinfra/types.h>
#include <vppinfra/vec.h>
#include <vnet/ip/ip4_packet.h>
#include <vnet/gre/gre.h>
#include <time.h>

#include "upf.h"
#include <upf/upf_pfcp.h>
#include <upf/upf_pfcp_server.h>
#include "flowtable.h"
#include "flowtable_tcp.h"
#include <upf/upf_duplicate_buffer.h>
#include <upf/upf_5glan.h>

#define UPF_FLOWTABLE_VECTOR_SIZE 4
#define UPF_FLOWTABLE_PREFETCH_GAP 3

static u8 *
format_flowtable_input (u8 *s, va_list *args)
{
  CLIB_UNUSED (vlib_main_t * vm) = va_arg (*args, vlib_main_t *);
  CLIB_UNUSED (vlib_node_t * node) = va_arg (*args, vlib_node_t *);
  flow_trace_t *t = va_arg (*args, flow_trace_t *);
  ip4_header_t *ih4 = (ip4_header_t *)t->packet_data;
  u32 indent = format_get_indent (s);

  s = format (
      s,
      "FlowInfo - flow %u sw_if_index %d, next_index = %d duplicate_num %u\n%U%U\n%U%U\n%U%U",
      t->flow_idx, t->sw_if_index, t->next_index, t->dupl_num, format_white_space, indent,
      upf_format_flow_key, &t->key, format_white_space, indent, format_hex_bytes,
      &t->key, sizeof (t->key), format_white_space, indent,
      (ih4->ip_version_and_header_length & 0xF0) == 0x60 ? format_ip6_header
                                                         : format_ip4_header,
      t->packet_data, sizeof (t->packet_data));
  return s;
}

void upf_load_flow_info (vlib_main_t *vm, flowtable_main_t *fm, vlib_buffer_t *b,
                     flow_key_t *key, flow_entry_t *flow, uword is_reverse)
{
  if (upf_pfcp_session_is_valid (key->session_index))
    upf_buffer_opaque (b)->upf.session_index = key->session_index;
  else
    upf_buffer_opaque (b)->upf.session_index = ~0;
  upf_buffer_opaque (b)->upf.is_reverse = is_reverse;
  upf_buffer_opaque (b)->upf.flow_index = flow - fm->flows;
  upf_buffer_opaque (b)->upf.pdr_index = flow->pdr_index[is_reverse];
}

int
upf_lookup_flowtable_with_hash (flowtable_main_t *fm,
                                  clib_bihash_kv_64_8_t *kv, u64 hash,
                                  u64 *pvalue_entry)
{
  int res;
  clib_bihash_kv_64_8_t kv_result;
  kv_result.value = ~0ULL;

  res = clib_bihash_search_inline_2_with_hash_64_8 (&fm->flows_ht, hash, kv,
                                                    &kv_result) == 0;
  *pvalue_entry = kv_result.value;
  return res;
}

/* dummy flow used in case alloc fail */
__thread flow_entry_t g_offload_flow = {
    .pdr_index[FT_ORIGIN] = ~0,
    .pdr_index[FT_REVERSE] = ~0,
    .flowcache[FT_ORIGIN] = NULL,
    .flowcache[FT_REVERSE] = NULL,
    .lifetime = ~0,
    .thread_index = ~0,
};

u32 gtp_parse(void *data, tunnel_info_t *tunnel, gtpu_flags_t *v)
{
    gtpu_header_t *gtpu = data;

    /* Validate basic GTPU header */
    if (PREDICT_FALSE(!gtpu))
    {
        upf_warn ("gtp_parse: null data pointer");
        v->format_illegal = 1;
        return 0;
    }

    if (PREDICT_FALSE ((gtpu->ver_flags & GTPU_VER_MASK) != GTPU_V1_VER))
    {
        v->format_illegal = 1;
        upf_warn ("gtp version:0x%x not support, Teid:%u", gtpu->ver_flags, gtpu->teid);
        return 0;
    }

    v->gtpu = 1;
    if (PREDICT_FALSE(gtpu->type != GTPU_TYPE_GTPU))
        return 1;

    tunnel->id = gtpu->teid;
    tunnel->len = sizeof(gtpu_header_t);

    if (gtpu->ver_flags & GTPU_E_BIT)
    {
        u8 *next_ext_hdr_type = NULL;

        /* Handle different extension header types according to 3GPP TS 29.281 */
        switch (gtpu->next_ext_type)
        {
            case GTP_EX_TYPE_PDU_SESS:
            {
                pdu_sess_container_ex_t *container = (pdu_sess_container_ex_t *)(gtpu + 1);

                /* Validate container length to prevent buffer overflow */
                if (PREDICT_FALSE(!container->ext_hdr_len))
                {
                    upf_warn ("PDU Session Container extension header length is zero, Teid:%u", gtpu->teid);
                    v->format_illegal = 1;
                    return 0;
                }

                /* neil.fan@20221031 note: according to 29281 5.2.1 Figure 5.2.1-3 NOTE4:"For a GTP-PDU with Extension Headers,
                 * the PDU Session Container should be the first Extension Header."
                 */
                v->pdu_sess_container = 1;
                pdu_sess_info_flags_t *flags = &container->flags;
                v->pdu_type = flags->pdu_type;

                if (flags->pdu_type == UPF_DL)
                {
                    v->qfi = flags->dl_flag.qfi;
                    v->qmp = flags->dl_flag.qmp;
                }
                else if (flags->pdu_type == UPF_UL)
                {
                    v->qfi = flags->ul_flag.qfi;
                    v->qmp = flags->ul_flag.qmp;
                }
                else
                {
                    upf_warn ("pdu_type:0x%x is illegal, Teid:%u", flags->pdu_type, gtpu->teid);
                    v->format_illegal = 1;
                    return 0;
                }

                if (container->ext_hdr_len > 1)
                    upf_trace ("Teid:%u %U", gtpu->teid, upf_format_gtp_pdu_session_container, (gtpu + 1));

                v->container_len = GTP_EXT_LEN(container->ext_hdr_len);
                next_ext_hdr_type = (u8 *)container + GTP_EXT_NEXT_TYPE_OFFSET(v->container_len);
                tunnel->len += v->container_len;
                if (PREDICT_TRUE(!next_ext_hdr_type[0]))
                    return 0;
                break;
            }

            case GTP_EX_TYPE_NO_MORE_EX_HDR:
            {
                /* This is a valid case - GTPU packet with E bit set but no actual extension headers
                 * According to 3GPP TS 29.281, this means the packet has sequence number, N-PDU number,
                 * or next extension header type fields present but no extension headers follow.
                 */
                upf_debug ("GTPU packet with E bit set but no extension headers, Teid:%u", gtpu->teid);
                /* No extension headers to process, continue with normal packet processing */
                return 0;
            }

            case GTP_EX_TYPE_SCI:
            case GTP_EX_TYPE_UPD_PORT:
            {
                /* Handle other known extension header types */
                upf_debug ("GTPU extension type 0x%x not fully supported, skipping, Teid:%u",
                          gtpu->next_ext_type, gtpu->teid);
                /* For now, we don't process these extension headers but don't treat as error */
                return 0;
            }

            default:
            {
                /* Unknown or reserved extension header type */
                if (GTPU_EXT_TYPE_IS_VALID(gtpu->next_ext_type))
                {
                    upf_debug ("GTPU extension type %s (0x%x) not fully implemented, Teid:%u",
                              GTPU_EXT_TYPE_NAME(gtpu->next_ext_type), gtpu->next_ext_type, gtpu->teid);
                }
                else
                {
                    upf_warn ("Unknown GTPU extension type:0x%x, Teid:%u", gtpu->next_ext_type, gtpu->teid);
                }
                /* Don't mark as illegal format - just skip processing extension headers */
                return 0;
            }
        }

        u8 ext_header_num = 1;
        while (next_ext_hdr_type && *next_ext_hdr_type)
        {
            gtpu_ext_header_t *hdr = (gtpu_ext_header_t *)(next_ext_hdr_type + 1);
            if (PREDICT_FALSE(!hdr->len))
            {
                v->format_illegal = 1;
                upf_debug("GTP extension header length is zero error, ext_header_num:%u", ext_header_num);
                break;
            }

            u16 content_len = GTP_EXT_LEN(hdr->len);
            next_ext_hdr_type = (u8 *)hdr + GTP_EXT_NEXT_TYPE_OFFSET(content_len);
            tunnel->len += content_len;

            /* support max 4 extension header (maybe extension in future), to avoid infinite loop */
            if (++ext_header_num > 4)
            {
                v->format_illegal = 1;
                upf_debug("GTP extension header number:%u over the max supported", ext_header_num);
                break;
            }
        }
    }
    else if (!(gtpu->ver_flags & GTPU_E_S_PN_BIT))
    {
        tunnel->len -= GTPU_V1_HDR_PT_E_S_LEN;
    }
    return 0;
}

u8 *format_rfc5905_u64_timestamp(u8 *s, va_list *args)
{
    upf_64bit_timestamp_t *v = va_arg (*args, upf_64bit_timestamp_t *);

    s = format (s, "0x%x[s] 0x%x[picos] ", v->sec, v->psec);

    struct timeval tp;
    tp.tv_sec = v->sec;
    tp.tv_usec = v->psec >> 2;
    struct tm *tm = localtime(&tp.tv_sec);

    s = format (s, "%04d %02d %02d %02d:%02d:%02d.%03ld.%03ld", tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday,
        tm->tm_hour, tm->tm_min, tm->tm_sec, tp.tv_usec / 1000, tp.tv_usec % 1000);

    return s;
}

u8 *format_rfc5905_64bit_timestamp(u8 *s, va_list *args)
{
    u8 *v = va_arg (*args, u8 *);
    upf_64bit_timestamp_t t;

    t.sec = clib_host_to_net_u32(*(u32 *)&v[0]);
    t.psec = clib_host_to_net_u32(*(u32 *)&v[4]);
    s = format (s, "%U", format_rfc5905_u64_timestamp, &t);

    return s;
}

u8 *format_gtp_extension_padding(u8 *s, va_list *args)
{
    u8 *p = va_arg (*args, u8 *);
    u8 *end = va_arg (*args, u8 *);

    if (p < end - 1)
    {
        s = format (s, "  Padding: %u", p[0]);
        p += 1;
    
        u8 pad_cnt = 2;
        do
        {
            if (p == end - 1)
                break;
    
            s = format (s, " %u", p[0]);
            p += 1;
        } while (--pad_cnt);
        s = format (s, "\n");
    }

    if (p == end - 1)
        s = format (s, "  Next Extension Header Type:0x%x \n", p[0]);
    else
        s = format (s, "  Extension format illegal, p:%p, end:%p\n", p, end);

    return s;
}

/* Helper function to validate GTPU extension header bounds */
static inline int
gtpu_validate_extension_header_bounds(gtpu_header_t *gtpu, u8 *ext_hdr_start, u16 ext_hdr_len, u16 packet_len)
{
    /* Basic bounds checking to prevent buffer overflow */
    u8 *packet_start = (u8 *)gtpu;
    u8 *ext_hdr_end = ext_hdr_start + ext_hdr_len;
    u8 *packet_end = packet_start + packet_len;

    if (PREDICT_FALSE(ext_hdr_start < packet_start ||
                      ext_hdr_end > packet_end ||
                      ext_hdr_len == 0))
    {
        return 0; /* Invalid bounds */
    }

    return 1; /* Valid bounds */
}

u8 *upf_format_gtp_pdu_session_container(u8 *s, va_list *args)
{
    pdu_sess_container_ex_t *v = va_arg (*args, pdu_sess_container_ex_t *);

    u16 len = v->ext_hdr_len << 2;
    s = format (s, "PDU Session Container: \n  length:%u  real-length:%u\n", v->ext_hdr_len, len);
    u8 *end = (u8 *)v + len;

    s = format (s, "  hex: %U", format_hex, (u8 *)v, len);

    if (v->flags.pdu_type == UPF_DL)
    {
        dl_pdu_sess_flags_t *dl_flag = &v->flags.dl_flag;
        s = format (s, "  PDU Type:DL  QMP:%u  SNP:%u  MSNP:%u  SPARE:%u\n", dl_flag->qmp, dl_flag->snp, dl_flag->msnp);
        s = format (s, "  PPP:%u        RQI:%u   QFI:%u\n", dl_flag->ppp, dl_flag->rqi, dl_flag->qfi);

        u8 *p = v->data;
        if (dl_flag->ppp)
        {
            s = format (s, "  PPI:%u\n", ((dl_pdu_sess_info_octet2nd_t *)p)->ppi);
            p += 1;
        }

        if (dl_flag->qmp)
        {
            s = format (s, "  DL Sending Time Stamp: %U\n", format_rfc5905_64bit_timestamp, p);
            p += 8;
        }

        if (dl_flag->snp)
        {
            s = format (s, "  DL QFI Sequence Number: %u %u %u\n", p[0], p[1], p[2]);
            p += 3;
        }

        if (dl_flag->msnp)
        {
            s = format (s, "  DL MBS QFI Sequence Number: %u\n", clib_host_to_net_u32(*(u32 *)&p[0]));
            p += 4;
        }

        s = format (s, "%U", format_gtp_extension_padding, p, end);
    }
    else if (v->flags.pdu_type == UPF_UL)
    {
        ul_pdu_sess_flags_t *ul_flag = &v->flags.ul_flag;
        s = format (s, "  PDU Type:UL  QMP:%u  DL Delay Ind:%u  UL Delay Ind:%u  SNP:%u\n",
                    ul_flag->qmp, ul_flag->dl_delay_ind, ul_flag->ul_delay_ind, ul_flag->snp);
        s = format (s, "  N3N9 Delay Ind:%u  New IE Flag:%u  QFI:%u\n", ul_flag->n3_n9_delay_ind,
                    ul_flag->new_ie_flag, ul_flag->qfi);

        u8 *p = v->data;
        if (ul_flag->qmp)
        {
            s = format (s, "  DL Sending Time Stamp Repeated: %U\n", format_rfc5905_64bit_timestamp, p);
            s = format (s, "  DL Received Time Stamp        : %U\n", format_rfc5905_64bit_timestamp, p + 8);
            s = format (s, "  UL Sending Time Stamp         : %U\n", format_rfc5905_64bit_timestamp, p + 16);
            p += 24;
        }

        if (ul_flag->dl_delay_ind)
        {
            s = format (s, "  DL Delay Result: %u\n", clib_host_to_net_u32(*(u32 *)&p[0]));
            p += 4;
        }

        if (ul_flag->ul_delay_ind)
        {
            s = format (s, "  UL Delay Result: %u\n", clib_host_to_net_u32(*(u32 *)&p[0]));
            p += 4;
        }

        if (ul_flag->snp)
        {
            s = format (s, "  UL QFI Sequence Number: %u %u %u\n", p[0], p[1], p[2]);
            p += 3;
        }

        if (ul_flag->n3_n9_delay_ind)
        {
            s = format (s, "  N3N9 Delay Result: %u\n", clib_host_to_net_u32(*(u32 *)&p[0]));
            p += 4;
        }

        if (ul_flag->new_ie_flag)
        {
            pdu_sess_info_new_flags_t *f = (pdu_sess_info_new_flags_t *)p;
            s = format (s, "  NEW IE FLAG7:%u Flag6:%u Flag5:%u Flag4:%u Flag3:%u Flag2:%u Flag1:%u Flag0:%u\n", 
                        f->new_flag7, f->new_flag6, f->new_flag5, f->new_flag4, f->new_flag3, f->new_flag2,
                        f->new_flag1, f->new_flag0);
            p += 1;
            if (f->new_flag7)
            {
                s = format (s, "  SPARE:%u  D1 UL PDCP Delay Result Ind:%u\n",
                            f->spare3, f->d1_ul_pdcp_delay_result_ind);
                p += 1;
            }
        }
        s = format (s, "%U", format_gtp_extension_padding, p, end);
    }
    else
    {
        s = format (s, "  PDU Type:%u Error, refer to TS38415 5.5.2", v->flags.pdu_type);
        goto err;
    }

    return s;

err:
    s = format (s, "  Extension data coding error.\n");
    return s;
}

u32 vxlan_parse(void *data, tunnel_info_t *tunnel)
{
    vxlan_header_t *vxlan = data;
    tunnel->id = vnet_get_vni(vxlan);
    tunnel->len = sizeof(vxlan_header_t);

    return 0;
}

u32 gre_parse(void *data, tunnel_info_t *tunnel)
{
    gre_header_t *gre = data;
    tunnel->id = 0;
    tunnel->len = sizeof(gre_header_t);

    if (gre->flags_and_version)
    {
        /* 1) need to support c/k/s flags to fix tunnel->len */

        /* 2) maybe support tunnel->id = key if k flag set, refer to rfc2890 "Extensions to GRE header" */
    }

    return 0;
}

upf_session_t *upf_get_session_by_tunnel (u32 tunnel_type, u32 instance)
{
    session_by_tunnel_t kv;

    kv.k.type = tunnel_type;
    kv.k.instance_id = instance;
    kv.v.as_u64 = ~0;

    if (PREDICT_TRUE(!clib_bihash_search_inline_8_8 (&g_upf_main.session_by_tunnel, (clib_bihash_kv_8_8_t *)&kv)))
        return sx_get_by_index(kv.v.session_index);

    return NULL;
}

static void parse_grab_msg(vlib_buffer_t *buffer, flow_key_t *key, u8 is_reverse, upf_single_trace_push_t *grab, u8 is_ip4)
{

  //grab->session_id = upf_buffer_opaque (buffer)->upf.session_index;

  if (is_ip4)
  {
    grab->src_addr.ip4.data_u32 = key->ip[FT_ORIGIN ^ is_reverse].ip4.data_u32;
    grab->dst_addr.ip4.data_u32 = key->ip[FT_REVERSE ^ is_reverse].ip4.data_u32;

    grab->src_port = key->port[FT_ORIGIN ^ is_reverse];
    grab->dst_port = key->port[FT_REVERSE ^ is_reverse];
  }
  else
  {
    grab->src_addr.ip6.as_u64[0] = key->ip[FT_ORIGIN ^ is_reverse].ip6.as_u64[0];
    grab->src_addr.ip6.as_u64[1] = key->ip[FT_ORIGIN ^ is_reverse].ip6.as_u64[1];
    grab->dst_addr.ip6.as_u64[0] = key->ip[FT_REVERSE ^ is_reverse].ip6.as_u64[0];
    grab->dst_addr.ip6.as_u64[1] = key->ip[FT_REVERSE ^ is_reverse].ip6.as_u64[1];
  }

  grab->is_ip4 = is_ip4;
  grab->ne_instance_id = g_local_ne_id;

  return;
}

int upf_dp_check_userid(upf_grab_msg_t grab_msg, upf_session_t *sess)
{
  pfcp_user_id_t *user_id;

  user_id = &sess->user_id;

  if (grab_msg.user_id_type == UPF_USER_ID_INVALID)
  {
    return 0;
  }

  if (((grab_msg.user_id_type == UPF_USER_ID_MSISDN) && memcmp(grab_msg.user_id, user_id->msisdn_str, sizeof(grab_msg.user_id)) == 0) || 
      ((grab_msg.user_id_type == UPF_USER_ID_IMSI) && memcmp(grab_msg.user_id, user_id->imsi_str, sizeof(grab_msg.user_id)) == 0) || 
      ((grab_msg.user_id_type == UPF_USER_ID_IMEI) && memcmp(grab_msg.user_id, user_id->imei_str, sizeof(grab_msg.user_id)) == 0))
  {
    return 0;
  }
  upf_err("## wuwei grab_msg.user_id_type:%u, grab_msg.user_id:%s, msisdn_str:%s, imei_str:%s, imsi_str:%s\n", 
          grab_msg.user_id_type, grab_msg.user_id, user_id->msisdn_str, user_id->imei_str, user_id->imsi_str);

  return -1;
}

int upf_dp_check_ip(upf_grab_msg_t grab_msg, upf_single_trace_push_t *grab, u8 is_ip4)
{
  int ret = -1;

  upf_debug("######## wuwei g_upf_single_trace:[src ip:%U dst ip:%U]\n", format_ip46_address, &grab_msg.src_addr, IP46_TYPE_IP4, 
    format_ip46_address, &grab_msg.dst_addr, IP46_TYPE_IP4);

  upf_debug("######## wuwei pkg:[src ip:%U dst ip:%U]\n", format_ip46_address, &grab->src_addr, IP46_TYPE_IP4, 
    format_ip46_address, &grab->dst_addr, IP46_TYPE_IP4);

  //ip4ï¿½ï¿½ï¿½ï¿½Ð£ï¿½ï¿½
  if (is_ip4)
  {
    if (grab_msg.src_addr.ip4.data_u32 != ~0 && grab_msg.dst_addr.ip4.data_u32 != ~0)
    {
      if ((grab_msg.src_addr.ip4.data_u32 == grab->src_addr.ip4.data_u32 && 
          grab_msg.dst_addr.ip4.data_u32 == grab->dst_addr.ip4.data_u32) ||
          (grab_msg.src_addr.ip4.data_u32 == grab->dst_addr.ip4.data_u32 && 
          grab_msg.dst_addr.ip4.data_u32 == grab->src_addr.ip4.data_u32))
      {
        ret = 0;
      }
    }
    else if (grab_msg.src_addr.ip4.data_u32 != ~0)
    {
      upf_err("######### wuwei \n");
      if (grab_msg.src_addr.ip4.data_u32 == grab->src_addr.ip4.data_u32 || 
          grab_msg.src_addr.ip4.data_u32 == grab->dst_addr.ip4.data_u32)
      {
        ret = 0;
      }
    }
    else if (grab_msg.dst_addr.ip4.data_u32 != ~0)
    {
      if (grab_msg.dst_addr.ip4.data_u32 == grab->src_addr.ip4.data_u32 || 
          grab_msg.dst_addr.ip4.data_u32 == grab->dst_addr.ip4.data_u32)
      {
        ret = 0;
      }
    }
    else
    {
      //È«F,ï¿½ï¿½ï¿½ï¿½ÒªÆ¥ï¿½ï¿½ip
      ret = 0;
    }
  }
  else   //ip6ï¿½ï¿½ï¿½ï¿½Ð£ï¿½ï¿½
  {
    if (grab_msg.src_addr.ip6.as_u64[0] != ~0 && grab_msg.src_addr.ip6.as_u64[1] != ~0 && 
          grab_msg.dst_addr.ip6.as_u64[0] != ~0 && grab_msg.dst_addr.ip6.as_u64[1] != ~0)
    {
      if ((ip6_address_compare(&grab_msg.src_addr.ip6, &grab->src_addr.ip6) == 0 && 
          ip6_address_compare(&grab_msg.dst_addr.ip6, &grab->dst_addr.ip6) == 0) || 
          (ip6_address_compare(&grab_msg.src_addr.ip6, &grab->dst_addr.ip6) == 0 && 
          ip6_address_compare(&grab_msg.dst_addr.ip6, &grab->src_addr.ip6) == 0))
      {
        ret = 0;
      }
    }
    else if (grab_msg.src_addr.ip6.as_u64[0] != ~0 && grab_msg.src_addr.ip6.as_u64[1] != ~0)
    {
      if (ip6_address_compare(&grab_msg.src_addr.ip6, &grab->src_addr.ip6) == 0 && 
          ip6_address_compare(&grab_msg.src_addr.ip6, &grab->dst_addr.ip6) == 0)
      {
        ret = 0;
      }
    }
    else if (grab_msg.dst_addr.ip6.as_u64[0] != ~0 && grab_msg.dst_addr.ip6.as_u64[1] != ~0)
    {
      if (ip6_address_compare(&grab_msg.dst_addr.ip6, &grab->src_addr.ip6) == 0 && 
          ip6_address_compare(&grab_msg.dst_addr.ip6, &grab->dst_addr.ip6) == 0)
      {
        ret = 0;
      }
    }
    else
    {
      //È«F,ï¿½ï¿½ï¿½ï¿½ÒªÆ¥ï¿½ï¿½ip
      ret = 0;
    }
  }
  upf_err("######### wuwei ret:%d\n", ret);

  return ret;
}

int upf_dp_check_port(upf_grab_msg_t grab_msg, upf_single_trace_push_t *grab)
{
  int ret = -1;
  u16 task_src_port = 0, task_dst_port = 0;
  task_src_port = clib_host_to_net_u16(grab_msg.src_port);
  task_dst_port = clib_host_to_net_u16(grab_msg.dst_port);

  if (task_src_port != (u16)~0 && task_dst_port != (u16)~0)
  {
    if ((task_src_port == grab->src_port && task_dst_port == grab->dst_port) ||
      (task_src_port == grab->dst_port && task_dst_port == grab->src_port))
    {
      ret = 0;
    }
  }
  else if (task_src_port !=(u16) ~0)
  {
    if (task_src_port == grab->src_port || task_src_port == grab->dst_port)
    {
      ret = 0;
    }
  }
  else if (task_dst_port != (u16)~0)
  {
    if (task_dst_port == grab->src_port || task_dst_port == grab->dst_port)
    {
      ret = 0;
    }
  }
  else
  {
    //È«F,ï¿½ï¿½ï¿½ï¿½ÒªÆ¥ï¿½ï¿½ip
    ret = 0;
  }
  upf_err("######### wuwei ret:%d task.src_port:%d, grab_msg.dst_port:%d, pkg->src_port:%d, grab->dst_port:%d\n", 
      ret, task_src_port, task_dst_port, grab->src_port, grab->dst_port);

  return ret;
}

int upf_dp_check_element_id(upf_grab_msg_t grab_msg, upf_single_trace_push_t *grab)
{
  int ret = -1;

  if (~0 == grab_msg.ne_instance_id || grab_msg.ne_instance_id == g_local_ne_id)
  {
    ret = 0;
  }
  upf_err("######### wuwei ret:%d\n", ret);

  return ret;
}

int upf_dp_check_interface_type(upf_grab_msg_t grab_msg, upf_session_t *sess, upf_single_trace_push_t *grab)
{
  int ret = -1;
  struct rules *active;
  upf_pdr_t *pdr = NULL;
  upf_far_t *far = NULL;
  active = upf_get_rules (sess, SX_ACTIVE);
  pdr = upf_get_pdr_by_id (active, grab->pdr_id);
  far = upf_get_far_by_id (active, grab->far_id);

  upf_debug ("packets flow from %U to %U, grab interface_type:%u/n", upf_format_tgpp_interface_type, &pdr->pdi.source_interface_type, 
      upf_format_tgpp_interface_type, &far->forward.dest_interface_type, grab_msg.interface_type);

  if (grab_msg.interface_type == 0)
    return 0;

  upf_err("########## wuwei ret:%d, grab_msg.interface_type:%d, UPF_GRAB_MSG_INTERFACE_N3:%d\n", ret, grab_msg.interface_type, UPF_GRAB_MSG_INTERFACE_N3);

  if (grab->direction == UPF_PKT_DIRECTION_IN || grab->direction == UPF_PKT_DIRECTION_ANY)
  {
    if (11 <= pdr->pdi.source_interface_type && pdr->pdi.source_interface_type <= 14) //ï¿½ï¿½ï¿½ÎªN3
    {
      if (grab_msg.interface_type & UPF_SINGLE_TRACE_INTF_N3)
        ret = 0;
    }
    else if (pdr->pdi.source_interface_type == 15) //ï¿½ï¿½ï¿½ÎªN9
    {
      if (grab_msg.interface_type & UPF_SINGLE_TRACE_INTF_N9)
        ret = 0;
    }
    else if (pdr->pdi.source_interface_type == 17) //ï¿½ï¿½ï¿½ÎªN6
    {
      if (grab_msg.interface_type & UPF_SINGLE_TRACE_INTF_N6)
        ret = 0;
    }
    else
    {
      upf_err ("Unknown source interface type!");
    }
  }
  else
  {
    if (11 <= far->forward.dest_interface_type && far->forward.dest_interface_type <= 14) //ï¿½ï¿½ï¿½ï¿½ÎªN3
    {
      if (grab_msg.interface_type & UPF_SINGLE_TRACE_INTF_N3)
        ret = 0;
    }
    else if (far->forward.dest_interface_type == 15) //ï¿½ï¿½ï¿½ï¿½ÎªN9
    {
      if (grab_msg.interface_type & UPF_SINGLE_TRACE_INTF_N9)
        ret = 0;
    }
    else if (far->forward.dest_interface_type == 17) //ï¿½ï¿½ï¿½ï¿½ÎªN6
    {
      if (grab_msg.interface_type & UPF_SINGLE_TRACE_INTF_N6)
        ret = 0;
    }
    else
    {
      upf_err ("Unknown source interface type!");
    }
  }

  upf_err("########## wuwei ret:%d\n", ret);
  return ret;
}

void upf_dp_grab_msg_update(upf_session_t *sess, upf_single_trace_push_t *grab, u8 is_ip4)
{
  int i = 0, j = 0;

  memset(sess->single_trace_list, ~0, sizeof(sess->single_trace_list));

  for (i = 0; i < UPF_U16_MAX_SIZE; i++)
  {
    if (g_upf_single_trace[i].is_used == 0)
    {
      continue;
    }

    //user_id/ip/port/interface_type
    if ((upf_dp_check_userid(g_upf_single_trace[i], sess) == -1) || 
      (upf_dp_check_ip(g_upf_single_trace[i], grab, is_ip4) == -1) || 
      (upf_dp_check_port(g_upf_single_trace[i], grab) == -1) || 
      (upf_dp_check_element_id(g_upf_single_trace[i], grab) == -1))
    {
      upf_err("## wuwei Failed to update sess->single_trace_list task_id:%u", g_upf_single_trace[i].ne_instance_id);
      continue;
    }

    for (j = 0; j < UPF_STRING_LEN8; j++)
    {
      // Modify for taskid uint16 by liupeng on 2021-06-09 below
      if (sess->single_trace_list[j] == (u16)~0)
      // Modify for taskid uint16 by liupeng on 2021-06-09 above
      {
        sess->single_trace_list[j] = i;
        break;
      }
    }

    if (j == UPF_STRING_LEN8)
    {
      upf_err("session->single_trace_list is full, the max num = %d\n", UPF_STRING_LEN8);
      return;
    }
  }

  sess->single_trace_flag = g_single_trace_flag;
  return;
}
// Modify for taskid uint16 by liupeng on 2021-06-09 below
static void upf_dp_fill_grab_msg_push(vlib_buffer_t *buffer, u16 task_id, upf_single_trace_push_t *push_msg, u8 is_ip4)
// Modify for taskid uint16 by liupeng on 2021-06-09 above
{  
  if ((buffer == NULL) || (push_msg == NULL))
  {
      upf_err("buffer or push_msg is NULL .\n");
      return;
  }
  // Add for trace push by liupeng on 2021-07-09 below
  flowtable_main_t *fm = &g_flowtable_main;  
  u32 cpu_index = os_get_thread_index ();
  flowtable_main_per_cpu_t *fmt = &fm->per_cpu[cpu_index];
  memset(fmt->traceData, 0, TRACE_MAX_SIZE);
  // Add for trace push by liupeng on 2021-07-09 above
  struct timeval subtle_time;
    
  push_msg->task_id = task_id;
  push_msg->utc_time = time(NULL);
  gettimeofday(&subtle_time, NULL);
  push_msg->subtle_time = subtle_time.tv_usec;
  //push_msg->protocol = 8805;//PFCPÐ­ï¿½ï¿½,ï¿½ï¿½ï¿½ï¿½Ê±ï¿½Ë¿Úºï¿½
  push_msg->ne_instance_id = g_local_ne_id; //ï¿½ï¿½ï¿½ï¿½È«ï¿½Ö´æ´¢,ï¿½ï¿½Ö±ï¿½Ó»ï¿½È¡
  push_msg->raw_data_len = buffer->current_data + buffer->current_length;
  if (push_msg->direction == UPF_PKT_DIRECTION_OUT) 
  {

      //ÏÈ°ÑmacÍ·»ñÈ¡µ½
      clib_memcpy(fmt->traceData, buffer->data, sizeof(ethernet_header_t));
      if (is_ip4)
      {
          fmt->traceData[12] = 0x08;
          fmt->traceData[13] = 0x00;
      }
      else
      {
          fmt->traceData[12] = 0x86;
          fmt->traceData[13] = 0xdd;
      }
      push_msg->raw_data_len = buffer->current_length + sizeof(ethernet_header_t);
      //ÔÚ°Ñ¼ÓÉÏµÄgtpÍ·µÄÏûÏ¢½øÐÐ¿½±´
      clib_memcpy(fmt->traceData+sizeof(ethernet_header_t), buffer->data+buffer->current_data, buffer->current_length);
      //×îºó°Ñ×é×°µÄÏûÏ¢¸ú×ÙÄÚÈÝ¿½±´¸øÉÏ±¨µÄ½á¹¹
      push_msg->raw_data = (u8 *)fmt->traceData;
      
  }
  else
  {
     push_msg->raw_data = buffer->data;
  }
  // Add for N3 N6 trace by liupeng on 2021-06-16 above
  //memcpy(push_msg->raw_data, buffer->data, (push_msg->raw_data_len + 1));

  return;
}

void upf_dp_grab_msg_push(vlib_buffer_t *buffer, upf_session_t *sess, upf_single_trace_push_t *grab, u8 is_ip4)
{
  int i = 0;
  u8 is_push = 0;
  // Modify for taskid uint16 by liupeng on 2021-06-09 below
  u16 task_id = ~0;
  // Modify for taskid uint16 by liupeng on 2021-06-09 above
  ip4_header_t *ip4;
  ip6_header_t *ip6;
  udp_header_t *udp;
  struct rules *active;
  upf_far_t *far = NULL;
  upf_pdr_t *pdr = NULL;
  bool gtpflag = false;
  if (NULL == buffer || NULL == sess || NULL == grab)
  {
    upf_err("Invalid param");
    return;
  }

  active = upf_get_rules (sess, SX_ACTIVE);
  far = upf_get_far_by_id (active, grab->far_id);
  if (NULL == far)
  {
      upf_err("not find far id:%d", grab->far_id);
      return;
  }

  pdr = upf_get_pdr_by_id (active, grab->pdr_id);
  if (NULL == pdr)
  {
      upf_err("not find pdr id:%d", grab->pdr_id);
      return;
  }

  if (is_ip4)
  {
    ip4 = vlib_buffer_get_current (buffer);
    udp = (udp_header_t *)ip4_next_header (ip4);

    if (IP_PROTOCOL_UDP == ip4->protocol && UPF_PFCP_PROTOCOL_PORT == udp->dst_port)
    {
      return;
    }

    for (i = 0; i < UPF_STRING_LEN8; i++)
    {
      // Modify for taskid uint16 by liupeng on 2021-06-09 below
      if (sess->single_trace_list[i] != (u16)~0)
      // Modify for taskid uint16 by liupeng on 2021-06-09 above
      {
        task_id = sess->single_trace_list[i];

        //user_id/ip/port/interface_type
        if ((upf_dp_check_userid(g_upf_single_trace[task_id], sess) == -1) || 
          (upf_dp_check_ip(g_upf_single_trace[task_id], grab, is_ip4) == -1) || 
          (upf_dp_check_port(g_upf_single_trace[task_id], grab) == -1) || 
          (upf_dp_check_element_id(g_upf_single_trace[task_id], grab) == -1) ||
          (upf_dp_check_interface_type(g_upf_single_trace[task_id], sess, grab) == -1))
        {
          continue;
        }

        if (ip4->protocol == IP_PROTOCOL_UDP && udp->dst_port == clib_host_to_net_u16 (UDP_DST_PORT_GTPU))
        {
          //gtp
          gtpflag = true;
          upf_trace("[signal information][%s] time:%lu.%u, imsi:%s, msisdn:%s, imei:%s, up_ip:%U, inner_src_ip:%U," 
            " inner_dst_ip:%U, outer_src_ip:%U, outer_dst_ip:%U, pdr:%d, far:%d", 
            grab->direction == UPF_PKT_DIRECTION_IN ? "IN":"OUT",grab->utc_time, grab->subtle_time, sess->user_id.imsi_str, 
            sess->user_id.msisdn_str, sess->user_id.imei_str, format_ip46_address, &sess->up_address, IP46_TYPE_IP4, 
            format_ip46_address, &grab->src_addr, IP46_TYPE_IP4, format_ip46_address, &grab->dst_addr, IP46_TYPE_IP4, 
            format_ip4_address, &ip4->src_address, format_ip4_address, &ip4->dst_address, grab->pdr_id, grab->far_id);
        }
        else
        {
          //no GTP header
          upf_trace("[signal information][%s] time:%lu.%u, imsi:%s, msisdn:%s, imei:%s, up_ip:%U, src_ip:%U, dst_ip:%U, pdr:%d, far:%d", 
            grab->direction == UPF_PKT_DIRECTION_IN ? "IN":"OUT", grab->utc_time, grab->subtle_time, sess->user_id.imsi_str, 
            sess->user_id.msisdn_str, sess->user_id.imei_str, format_ip46_address, &sess->up_address, IP46_TYPE_IP4, 
            format_ip46_address, &grab->src_addr, IP46_TYPE_IP4, format_ip46_address, &grab->dst_addr, IP46_TYPE_IP4, 
            grab->pdr_id, grab->far_id);
        }

        if (grab->direction == UPF_PKT_DIRECTION_IN)
        {
            if (pdr->pdi.source_interface_type >= 11 && pdr->pdi.source_interface_type <= 14)
            {
                //N3
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N3)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N3;
                    is_push = 1;
                }
            }
            else if (pdr->pdi.source_interface_type == 15)
            {
                //N9
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N9)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N9;
                    is_push = 1;
                }
                gtpflag = false;
            }
            else if (pdr->pdi.source_interface_type == 17)
            {
                //N6
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N6)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N6;
                    is_push = 1;
                }
            }
            else
            {
                upf_debug ("Unknown source interface type:%u !", pdr->pdi.source_interface_type);
            }
        }
        else
        {
            if (far->forward.dest_interface_type >= 11 && far->forward.dest_interface_type <= 14)
            {
                //N3
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N3)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N3;
                    is_push = 1;
                }
            }
            else if (far->forward.dest_interface_type == 15)
            {
                //N9
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N9)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N9;
                    is_push = 1;
                }
            }
            else if (far->forward.dest_interface_type == 17)
            {
                //N6
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N6)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N6;
                    is_push = 1;
                }
            }
            else
            {
                upf_debug ("Unknown destination interface type:%u !", far->forward.dest_interface_type);
            }

        }
        
        upf_dp_fill_grab_msg_push(buffer, task_id, grab, is_ip4);
        if (is_push)
        {
            upf_pfcp_events_publish (PFCP_RPC_PUBLISH_SINGLE_TRACE_PUSH, sess, grab);
        }
      }
    }
  }
  else
  {
    ip6 = vlib_buffer_get_current (buffer);
    udp = (udp_header_t *)ip6_next_header (ip6);

    if (IP_PROTOCOL_UDP == ip6->protocol && UPF_PFCP_PROTOCOL_PORT == udp->dst_port)
    {
      return;
    }

    for (i = 0; i < UPF_STRING_LEN8; i++)
    {
      // Modify for taskid uint16 by liupeng on 2021-06-09 below
      if (sess->single_trace_list[i] != (u16)~0)
      // Modify for taskid uint16 by liupeng on 2021-06-09 above
      {
        task_id = sess->single_trace_list[i];

        if (ip6->protocol == IP_PROTOCOL_UDP && udp->dst_port == clib_host_to_net_u16 (UDP_DST_PORT_GTPU))
        {
          //gtpï¿½ï¿½ï¿½ï¿½
          gtpflag = true;
          upf_trace("[signal information] time:%lu.%u, imsi:%s, msisdn:%s, imei:%s, up_ip:%U, inner_src_ip:%U, \
            inner_dst_ip:%U, outer_src_ip:%U, outer_dst_ip:%U, pdr:%d, far:%d", 
            grab->utc_time, grab->subtle_time, sess->user_id.imsi_str, sess->user_id.msisdn_str, sess->user_id.imei_str, 
            format_ip46_address, &sess->up_address, IP46_TYPE_IP6, format_ip46_address, &grab->src_addr, IP46_TYPE_IP6, 
            format_ip46_address, &grab->dst_addr, IP46_TYPE_IP6, format_ip6_address, &ip6->src_address, 
            format_ip6_address, &ip6->dst_address, grab->pdr_id, grab->far_id);
        }
        else
        {
          //no GTP header
          upf_trace("[signal information] time:%lu.%u, imsi:%s, msisdn:%s, imei:%s, up_ip:%U, src_ip:%U, dst_ip:%U, pdr:%d, far:%d", 
            grab->utc_time, grab->subtle_time, sess->user_id.imsi_str, sess->user_id.msisdn_str, sess->user_id.imei_str, 
            format_ip46_address, &sess->up_address, IP46_TYPE_IP6, format_ip46_address, &grab->src_addr, IP46_TYPE_IP6, 
            format_ip46_address, &grab->dst_addr, IP46_TYPE_IP6, grab->pdr_id, grab->far_id);
        }

        if (grab->direction == UPF_PKT_DIRECTION_IN)
        {
            if (upf_buffer_opaque (buffer)->upf.src_intf == SRC_INTF_ACCESS)
            {
                //N3
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N3)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N3;
                    is_push = 1;
                }
            }
            else if (upf_buffer_opaque (buffer)->upf.src_intf == SRC_INTF_CP && upf_buffer_opaque (buffer)->upf.teid != 0)
            {
                //N4
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N4)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N4;
                    is_push = 1;
                }
                
            }
            else if ((upf_buffer_opaque (buffer)->upf.src_intf == SRC_INTF_CORE && upf_buffer_opaque (buffer)->upf.teid != 0)
                     || gtpflag)
            {
                //N9
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N9)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N9;
                    is_push = 1;
                }
        
            }
            
            else
            {
                //N6
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N6)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N6;
                    is_push = 1;
                }
        
            }
            gtpflag = false;
        }
        else
        {
            if (far->forward.dst_intf == SRC_INTF_ACCESS)
            {
                //N3
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N3)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N3;
                    is_push = 1;
                }
            }
            else if (far->forward.dst_intf == SRC_INTF_CORE && far->forward.flags & FAR_F_OUTER_HEADER_CREATION)
            {
                //N9
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N9)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N9;
                    is_push = 1;
                }
            }
            else if (far->forward.dst_intf == SRC_INTF_CP && far->forward.flags & FAR_F_OUTER_HEADER_CREATION)
            {
                //N4
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N4)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N4;
                    is_push = 1;
                }
            }
            else
            {
                //N6
                if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N6)
                {
                    grab->interface_type = UPF_SINGLE_TRACE_INTF_N6;
                    is_push = 1;
                }
            }
        }
        upf_dp_fill_grab_msg_push(buffer, task_id, grab, is_ip4);

        if (is_push)
        {
            upf_pfcp_events_publish (PFCP_RPC_PUBLISH_SINGLE_TRACE_PUSH, sess, grab);
        }
      }
    }  
  }

  return;
}

static upf_ip_blacklist_t *
lookup_upf_device_blacklist (ip46_address_t *ip)
{
  upf_main_t *gtm = &g_upf_main;
  uword *p;

  if (pool_elts (gtm->upf_device_blacklist) == 0)
  {
      return NULL;
  }
    
  p = hash_get_mem (gtm->device_blacklist_index, ip);
  
  if (!p)
  {
      return NULL;
  }

  CHECK_POOL_IS_VALID_RET(gtm->upf_device_blacklist, p[0], NULL);
  return pool_elt_at_index (gtm->upf_device_blacklist, p[0]);
}

static void upf_flowtable_auxiliary_in(vlib_main_t *vm, vlib_buffer_t *b, flow_key_t *key, u8 is_reverse, u8 is_ip4)
{
    upf_main_t *um = &g_upf_main;
    upf_session_t *sess = NULL;
    struct rules *active;
    u32 sidx = ~0, pdr_idx = ~0;
    upf_single_trace_push_t grab;
    upf_pdr_t *pdr = NULL;
    upf_far_t *far = NULL;
    u32 current_time = (u32)vlib_time_now (vm);

    sidx = upf_buffer_opaque (b)->upf.session_index;
    pdr_idx = upf_buffer_opaque (b)->upf.pdr_index;
    if (sidx == (u32)~0 || pdr_idx == (u32)~0)
    {
        upf_err("sess index:%u, pdr index:%u", sidx, pdr_idx);
        return;
    }

    CHECK_POOL_IS_VALID_NORET(um->sessions, sidx);
    sess = pool_elt_at_index (um->sessions, sidx);
    active = upf_get_rules (sess, SX_ACTIVE);
    pdr = active->pdr + upf_buffer_opaque (b)->upf.pdr_index;
    far = upf_get_far_by_id (active, pdr->far_id);
    if (!far)
    {
        upf_err("far is null!");
        return;
    }

    if (g_upf_auxiliary_switch & UPF_STATISTICS_SWITCH)
    {
        upf_nwi_traffic_statistic(pdr, far, vlib_buffer_length_in_chain(vm, b), 0, 1);
        upf_dnn_traffic_statistic(b, pdr, far, vlib_buffer_length_in_chain(vm, b), 0, 1);
        upf_buffer_opaque (b)->upf.stat_flag = 1;
        u32 pfcp_thread_index = sess->thread_index - g_upf_main.first_pfcp_thread_index;
        upf_interface_traffic_statistic(sess, pfcp_thread_index, pdr, far, vlib_buffer_length_in_chain (vm, b), current_time, is_ip4);
    }

    if (g_single_trace_flag)
    {
        grab.pdr_id = pdr->id;
        grab.far_id = far->id;
        parse_grab_msg(b, key, is_reverse, &grab, is_ip4);
        //When the signaling tracking list changes, update the signaling list in the session
        if (sess->single_trace_flag != g_single_trace_flag)
        {
            upf_debug("grab msg list in seesion[sidx:%u] updata\n", sidx);
            grab.direction = UPF_PKT_DIRECTION_ANY;
            upf_dp_grab_msg_update(sess, &grab, is_ip4);
        }
        grab.direction = UPF_PKT_DIRECTION_IN;
        upf_dp_grab_msg_push(b, sess, &grab, is_ip4);
    }

    return;
}

static u16 upf_flowtable_auxiliary_out(vlib_main_t *vm, vlib_buffer_t *b, flow_key_t *key, u8 is_reverse, u8 is_ip4, u16 next)
{
    upf_main_t *um = &g_upf_main;
    upf_session_t *sess = NULL;
    struct rules *active;
    u32 sidx = ~0, pdr_idx = ~0;
    upf_single_trace_push_t grab;
    upf_pdr_t *pdr = NULL;
    upf_far_t *far = NULL;

    sidx = upf_buffer_opaque (b)->upf.session_index;
    pdr_idx = upf_buffer_opaque (b)->upf.pdr_index;
    if (sidx == (u32)~0 || pdr_idx == (u32)~0)
    {
        upf_err("sess index:%u, pdr index:%u", sidx, pdr_idx);
        return next;
    }

    CHECK_POOL_IS_VALID_RET(um->sessions, sidx, next);
    sess = pool_elt_at_index (um->sessions, sidx);
    active = upf_get_rules (sess, SX_ACTIVE);
    pdr = active->pdr + upf_buffer_opaque (b)->upf.pdr_index;
    far = upf_get_far_by_id (active, pdr->far_id);
    if (!far)
    {
        upf_err("far is null!");
        return next;
    }

    if (upf_nwi_mbr_process (pdr, far, vlib_buffer_length_in_chain(vm, b)))
    {
       next = FT_NEXT_DROP;
    }

    if (g_upf_auxiliary_switch & UPF_STATISTICS_SWITCH)
    {
        if (FT_NEXT_DROP != next)
        {
            upf_nwi_traffic_statistic(pdr, far, vlib_buffer_length_in_chain(vm, b), 0, 0);
            upf_dnn_traffic_statistic(b, pdr, far, vlib_buffer_length_in_chain(vm, b), 0, 0);
            upf_5glan_traffic_statistic(pdr, far, vlib_buffer_length_in_chain(vm, b), far->forward.nwi);
        }
        else
        {
            u32 pfcp_thread_index = sess->thread_index - g_upf_main.first_pfcp_thread_index;
            upf_interface_drop_statistic(sess, pfcp_thread_index, pdr, far, vlib_buffer_length_in_chain(vm, b), 0, is_ip4);
            upf_nwi_traffic_statistic(pdr, far, vlib_buffer_length_in_chain(vm, b), 1, 0);
            upf_dnn_traffic_statistic(b, pdr, far, vlib_buffer_length_in_chain(vm, b), 1, 0);
            upf_debug("pdrid: %u, farid:%u, src_intf:%u, dst_intf:%u", pdr->id, far->id, pdr->pdi.src_intf, far->forward.dst_intf);
        }
    }

    if (g_single_trace_flag)
    {
        //The fast path tracking information is pushed
        //The slow path response message is pushed at the upf_ip_process node and encap node
        grab.pdr_id = pdr->id;
        grab.far_id = far->id;
        parse_grab_msg(b, key, is_reverse, &grab, is_ip4);
        grab.direction = UPF_PKT_DIRECTION_OUT;
        upf_dp_grab_msg_push(b, sess, &grab, is_ip4);
    }

    return next;
}

static u32 upf_dnn_ue_ping_fun(vlib_buffer_t *b, flow_key_t *key)
{
    upf_main_t *um = &g_upf_main;
    upf_session_t *old_sess = NULL, *new_sess = NULL;
    u32 sidx = ~0;

    if (g_upf_dnn_ue_ping_switch)
    {
        if (key->proto == IP_PROTOCOL_ICMP && upf_buffer_opaque (b)->upf.session_index != 0)
        {
            sidx = upf_buffer_opaque (b)->upf.session_index;
            if (upf_pfcp_session_is_valid(sidx))
                old_sess = pool_elt_at_index (um->sessions, sidx);
            if (upf_pfcp_session_is_valid(key->session_index))
                new_sess = pool_elt_at_index (um->sessions, key->session_index);
            if (old_sess != NULL && new_sess != NULL)
            {
                if (vec_cmp(old_sess->dnn, new_sess->dnn) != 0)
                {
                    return 1;
                }
            }
        }
    }
    else
    {
        if (key->proto == IP_PROTOCOL_ICMP && upf_buffer_opaque (b)->upf.session_index != 0)
        {
            sidx = upf_buffer_opaque (b)->upf.session_index;
            if (upf_pfcp_session_is_valid(sidx))
                old_sess = pool_elt_at_index (um->sessions, sidx);
            if (upf_pfcp_session_is_valid(key->session_index))
                new_sess = pool_elt_at_index (um->sessions, key->session_index);
            if (old_sess != NULL && new_sess != NULL)
            {
                return 1;
            }
        }
    }

    return 0;
}

static void upf_dataguard_master_handle(vlib_main_t *vm, vlib_buffer_t *b, u16 *next, u8 is_ip4)
{
    if (g_upf_auxiliary_switch & UPF_PERFORMANCE_SWITCH)
        return;

    u32 rv = 0;
    upf_main_t *um = &g_upf_main;
    tunnel_info_t tunnel = {0};
    u32 is_encap_ip4 = 0;
    void *data = NULL;
    ip46_address_t ip_addr;
    clib_memset(&ip_addr, 0, sizeof(ip_addr));
    udp_header_t *udp = NULL;

    if (um->dataguard_switch == DATAGUARD_SWITCH_OFF || um->dataguard_status == DATAGUARD_STATUS_BACKUP)
    {
        return;
    }

    if (is_ip4)
    {
        ip4_header_t *ip4 = vlib_buffer_get_current (b);
        udp = (udp_header_t *)ip4_next_header (ip4);
        if (clib_net_to_host_u16(udp->dst_port) == UPF_PFCP_PROTOCOL_PORT)
            return;
        u32 session_index = upf_get_session_index_by_ip4 (vnet_buffer (b)->ip.fib_index, &ip4->src_address);
        if (session_index != ~0)
            return;//UL packet, session exist
        clib_memcpy(&ip_addr.ip4, &ip4->dst_address, sizeof(ip_addr.ip4));
        if (ip4->protocol == IP_PROTOCOL_GRE)
        {
            data = ip4_next_header (ip4);
            gre_parse(data, &tunnel);
            upf_buffer_opaque (b)->upf.data_offset = tunnel.len + ((u8 *)data - (u8 *)ip4);
            vlib_buffer_advance (b, upf_buffer_opaque (b)->upf.data_offset);
        }
        else if (ip4->protocol == IP_PROTOCOL_ICMP && ip_is_local(0, &ip_addr, is_ip4))
        {
            return;
        }
        else
        {
            rv = add_gre_header_l3(vm, b, um->dataguard_gre_id_v4, is_ip4 ? VNET_LINK_IP4 : VNET_LINK_IP6, &is_encap_ip4);
            if (rv)
            {
                upf_err("add_gre_header_l3 fail!");
            }
            if (is_encap_ip4 != is_ip4)
                *next = FT_NEXT_IP_LOOKUP;
        }
    }
    else
    {
        ip6_header_t *ip6 = vlib_buffer_get_current (b);
        udp = (udp_header_t *)ip6_next_header (ip6);
        if (clib_net_to_host_u16(udp->dst_port) == UPF_PFCP_PROTOCOL_PORT)
            return;
        u32 session_index = upf_get_session_index_by_ip6 (vnet_buffer (b)->ip.fib_index, &ip6->src_address);
        if (session_index != ~0)
            return;//UL packet, session exist
        clib_memcpy(&ip_addr.ip6, &ip6->dst_address, sizeof(ip_addr.ip6));
        if (ip6->protocol == IP_PROTOCOL_GRE)
        {
            data = ip6_next_header (ip6);
            gre_parse(data, &tunnel);
            upf_buffer_opaque (b)->upf.data_offset = tunnel.len + ((u8 *)data - (u8 *)ip6);
            vlib_buffer_advance (b, upf_buffer_opaque (b)->upf.data_offset);
        }
        else if (ip6->protocol == IP_PROTOCOL_ICMP6 && ip_is_local(0, &ip_addr, is_ip4))
        {
            return;
        }
        else
        {
            rv = add_gre_header_l3(vm, b, um->dataguard_gre_id_v6, is_ip4 ? VNET_LINK_IP4 : VNET_LINK_IP6, &is_encap_ip4);
            if (rv)
            {
                upf_err("add_gre_header_l3 fail!");
            }
            if (is_encap_ip4 != is_ip4)
                *next = FT_NEXT_IP_LOOKUP;
        }
    }

    return;
}

static uword
upf_flowtable_input (vlib_main_t *vm, vlib_node_runtime_t *node,
                         vlib_frame_t *frame, u8 is_ip4)
{
    u32 n_left_from, *from;
    flowtable_main_t *fm = &g_flowtable_main;
    u32 cpu_index = os_get_thread_index ();
    flowtable_main_per_cpu_t *fmt = &fm->per_cpu[cpu_index];
    u64 flow_index, flow_index_next;
    u64 *hash;
    u16 *next;
    u8 *is_reverse;
    clib_bihash_kv_64_8_t *kv;
    vlib_buffer_t **b;

#define _(sym, str) u64 CPT_##sym = 0;
  foreach_flowtable_error
#undef _
    from = vlib_frame_vector_args (frame);
    n_left_from = frame->n_vectors;

    u32 current_time = (u32)vlib_time_now (vm);
    upf_flowtable_timer_update (fm, fmt, current_time);

    vlib_get_buffers (vm, from, fmt->bufs, frame->n_vectors);

    is_reverse = fmt->is_reverse;
    hash = fmt->hashes;
    next = fmt->nexts;
    b = fmt->bufs;
    clib_memset(fmt->kvs, 0, sizeof(fmt->kvs));
    kv = fmt->kvs;

    u8 ori_ip_type = is_ip4;

    flow_mk_key_and_hash_xN (n_left_from, b, &is_ip4, is_reverse, kv, hash);

    upf_lookup_flowtable_with_hash (fm, &kv[0], hash[0], &flow_index_next);

    while (n_left_from > 0)
    {
        u32 next_index = 0;
        int recycled = 0;
        flow_entry_t *flow = NULL;
        flow_key_t *key = (flow_key_t *)kv[0].key;
        u8 is_reverse_tmp = is_reverse[0];
        u16 next_tmp = next[0];

        flow_index = flow_index_next;

        switch (n_left_from)
        {
        default:
          clib_bihash_prefetch_bucket_64_8 (&fm->flows_ht, hash[5]);
          /* fallthrough */
        case 5:
        case 4:
          clib_bihash_prefetch_data_64_8 (&fm->flows_ht, hash[3]);
          /* fallthrough */
        case 3:
        case 2:
          upf_lookup_flowtable_with_hash (fm, &kv[1], hash[1], &flow_index_next);
          /* fallthrough */
        case 1:
          vnet_feature_next (&next_index, b[0]);
          next_tmp = next_index;    //ip4-lookup
          if (g_upf_dnn_switch)
          {
              if (upf_dnn_ue_ping_fun(b[0], key))
              {
                  next_tmp = FT_NEXT_ERROR_IND;
                  goto trace;
              }
          }

          if(ori_ip_type != is_ip4)
          {
              next_tmp = FT_NEXT_IP_LOOKUP;
          }

          gtpu_flags_t *gtp_flags = &(upf_buffer_opaque (b[0])->upf.gtp_flags);
          if (gtp_flags->gtpu)
          {
              if (PREDICT_FALSE(gtp_flags->format_illegal))
              {
                  next_tmp = FT_NEXT_DROP;
                  goto trace;
              }
          }
          
          udp_header_t *udp;
          ip46_address_t ip;
          ip4_header_t *ip4 = vlib_buffer_get_current (b[0]);
          ip6_header_t *ip6 = vlib_buffer_get_current (b[0]);
          if (is_ip4)
          {
              ip46_address_set_ip4(&ip, &ip4->src_address);
              udp = (udp_header_t *)ip4_next_header (ip4);
              upf_buffer_node_ttl(b[0])->ttl.dscp = (ip4->tos >> 2) & 0x3F;
          }
          else
          {
              ip46_address_set_ip6(&ip, &ip6->src_address);
              udp = (udp_header_t *)ip6_next_header (ip6);
              upf_buffer_node_ttl(b[0])->ttl.dscp = ip6_dscp_network_order(ip6);

              if (KEY_LOG_SWITCH(KL_SWITCH_DST_NAT66) && (upf_buffer_opaque (b[0])->upf.src_intf == SRC_INTF_CORE))
              {   
                  u32 rx_sw_index = vnet_buffer (b[0])->sw_if_index[VLIB_RX];
                  ip46_address_t add46 = {0};
                  clib_memcpy(&add46.ip6, &ip6->dst_address, sizeof(ip6_address_t));
                  u8 ret = ip_interface_has_address (rx_sw_index, &add46, 0);
                  if ((ret == 0) && (!upf_dst_nat66_revert (&ip6->src_address)))
                      upf_nat_update_checksums (vm, b[0], 0);
              }
          }

          if (PREDICT_FALSE((++upf_buffer_node_ttl (b[0])->ttl.upf_process_node) >= g_upf_message_repeat_times))
          {
              upf_err ("upf ttl:%u exhaust, src:%U ", upf_buffer_node_ttl (b[0])->ttl.upf_process_node,
                format_ip46_address, &ip, IP46_TYPE_ANY);
              UPF_STATISTICS_ADD(UPF_TTL_EXHAUST);
              next_tmp = FT_NEXT_DROP;
              goto trace;
          }

          upf_ip_blacklist_t *device_blacklist = lookup_upf_device_blacklist(&ip);
          if (device_blacklist)
          {
              //upf_err ("IP: %U, packet src IP in device blacklist\n", format_ip46_address, &ip, IP46_TYPE_ANY);
              device_blacklist->pkg_drop++;
              next_tmp = FT_NEXT_DROP;
              goto trace;
          }

          if (upf_buffer_node_ttl(b[0])->ttl.is_eth)
          {
              next_tmp = FT_NEXT_FLOWTABLE_ETH;
              upf_buffer_opaque (b[0])->upf.session_index = key->session_index;
              goto trace;
          }

          if (PREDICT_TRUE(key->session_index != ~0))
          {
              if (PREDICT_FALSE((KEY_LOG_SWITCH (KL_SWITCH_REASS_GTP_DECAP_INNER)) && (upf_buffer_node_ttl(b[0])->ttl.is_fragment == 1)))
              {
                  if (upf_pfcp_session_is_valid (key->session_index))
                    upf_buffer_opaque (b[0])->upf.session_index = key->session_index;
                  else
                    upf_buffer_opaque (b[0])->upf.session_index = ~0;
                  //upf_buffer_opaque (b[0])->upf.is_reverse = is_reverse;
                  goto trace;
              }
          
              if (flow_index != ~0)
              {
                  flow = pool_elt_at_index (fm->flows, flow_index);
                  ++CPT_HIT;
              }
              else
              {
                  flow = upf_flowtable_entry_create (fm, fmt, &kv[0], current_time, is_reverse_tmp, &recycled);
                  if (PREDICT_FALSE (flow == NULL))
                  {
                      CPT_NORESOURCES++;
                      UPF_STATISTICS_ADD(NO_FLOW_RESOURCES);
                      flow = &g_offload_flow;
                      flow->lifetime = upf_flowtable_lifetime_calculate (fm, &flow->key);
                      if (!flow->lock)
                          clib_spinlock_init (&flow->lock);
                  }
                  else
                  {
                      ++CPT_CREATED;
                  }
                  CPT_RECYCLE += recycled;
              }

              /* fill opaque buffer with flow data */
              upf_load_flow_info (vm, fm, b[0], (flow_key_t *)&kv[0].key, flow, is_reverse_tmp);


              if (upf_buffer_node_ttl(b[0])->ttl.sx_tunnel_type > SESSION_TUNNEL_NONE)
              {
                  vlib_buffer_advance (b[0], upf_buffer_opaque (b[0])->upf.data_offset);
                  upf_buffer_opaque (b[0])->upf.data_offset = 0;

                  if (flow->flowcache[is_reverse_tmp] == NULL)
                      next_tmp = (is_v4_packet(vlib_buffer_get_current (b[0]))) ? FT_NEXT_IP4_INPUT : FT_NEXT_IP6_INPUT;
              }

              clib_spinlock_lock (&flow->lock);
              if (flow->flowcache[is_reverse_tmp] != NULL)
              {
                  if (!(g_upf_auxiliary_switch&UPF_PERFORMANCE_SWITCH))
                  {
                      upf_flowtable_auxiliary_in(vm, b[0], (flow_key_t *)&kv[0].key, is_reverse_tmp, is_ip4);
                  }

                  next_tmp = upf_flowcache_execute (vm, b[0], flow->flowcache[is_reverse_tmp], next_tmp, node, kv);
                  CPT_CACHE++;

                  if(ori_ip_type != is_ip4)
                  {
                      if(next_tmp == FT_NEXT_IP_LOOKUP)
                         next_tmp = next_index;
                  }

                  if (!(g_upf_auxiliary_switch&UPF_PERFORMANCE_SWITCH))
                  {
                      if (flow->vn_sess_idx != ~0)
                      {
                          upf_buffer_opaque (b[0])->upf.session_index = flow->vn_sess_idx;
                          upf_buffer_opaque (b[0])->upf.pdr_index = flow->vn_pdr_idx;
                      }
                      next_tmp = upf_flowtable_auxiliary_out(vm, b[0], (flow_key_t *)&kv[0].key, is_reverse_tmp, is_ip4, next_tmp);
                  }
              }
              clib_spinlock_unlock (&flow->lock);

              /* timer management */
              flow->active = current_time;

              /* flow statistics */
              ++flow->stats[is_reverse_tmp].pkts;
              flow->stats[is_reverse_tmp].bytes += b[0]->current_length;

              /* flowtable counters */
              ++CPT_THRU;
          }
          else
          {
              CPT_UNHANDLED++;
              flow = &g_offload_flow;
              flow->lifetime = upf_flowtable_lifetime_calculate (fm, &flow->key);

              upf_buffer_opaque (b[0])->upf.session_index = ~0;
              upf_buffer_opaque (b[0])->upf.flow_index = ~0;
              upf_buffer_opaque (b[0])->upf.pdr_index = ~0;
              if (udp->dst_port == clib_host_to_net_u16 (UDP_DST_PORT_GTPU))
              {
                  upf_err ("No session index found, send error indication\n");
                  UPF_STATISTICS_ADD(SENT_ERR_IND_TO_RAN);
                  next_tmp = FT_NEXT_ERROR_IND;
              }

              //There is no message matching the session in the downlink to report the message alarm
              if (g_upf_alarm_switch)
              {
                  upf_pkt_ip_t tmp;
                  memcpy(&tmp.src_ip, &key->ip[is_reverse_tmp].ip4, sizeof(tmp.src_ip));
                  memcpy(&tmp.dst_ip, &key->ip[is_reverse_tmp].ip4, sizeof(tmp.dst_ip));
                  if (upf_alarm_search(UPF_ALARM_DL_SESSION_CHECK_FAIL, &tmp) == (u32)~0)
                  {
                      if (upf_buffer_opaque (b[0])->upf.src_intf == SRC_INTF_CORE && is_ip4)
                      {
                          upf_alarm_msg_t *upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                          memset (upf_alarm, 0, sizeof (*upf_alarm));
                          upf_alarm->alarm_id = UPF_ALARM_DL_SESSION_CHECK_FAIL;
                          upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
                          upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                          upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_pkt_ip_t), CLIB_CACHE_LINE_BYTES);
                          memset (upf_alarm->data, 0, sizeof(upf_pkt_ip_t));
                          memcpy(upf_alarm->data, &tmp, sizeof(tmp));
                          for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
                          {
                              pfcp_send_sx_alarm_to_thread(i, upf_alarm);
                          }
                          upf_add_del_alarm_key(UPF_ALARM_DL_SESSION_CHECK_FAIL, &tmp, 1);
                      }
                  }
              }

              //For enhancements use case 10.2.2
              upf_dataguard_master_handle(vm, b[0], &next_tmp, is_ip4);
          }

trace:
          if (PREDICT_FALSE (b[0]->flags & VLIB_BUFFER_IS_TRACED))
          {
              flow_trace_t *t = vlib_add_trace (vm, node, b[0], sizeof (*t));
              clib_memcpy (&t->key, &kv[0].key, sizeof (t->key));
              t->flow_idx = flow ? flow - fm->flows:~0;
              t->sw_if_index = vnet_buffer (b[0])->sw_if_index[VLIB_RX];
              t->next_index = next_tmp;
              clib_memcpy (t->packet_data, vlib_buffer_get_current (b[0]), sizeof (t->packet_data));
          }

          next[0] = next_tmp;
          ++next;
          ++b;
          ++is_reverse;
          ++hash;
          ++kv;
          n_left_from -= 1;
        }
    }

    vlib_buffer_enqueue_to_next (vm, node, from, fmt->nexts, frame->n_vectors);

    // performance test need to close timer expire 
    if(g_upf_auxiliary_switch & UPF_FLOWTABLE_OLD_SWITCH)
    {
        /* handle expirations */
        CPT_TIMER_EXPIRE += upf_flowtable_timer_expire (fm, fmt, current_time);
    }

#define _(sym, str)  vlib_node_increment_counter (vm, node->node_index, FLOWTABLE_ERROR_##sym, CPT_##sym);
    foreach_flowtable_error
#undef _
    return frame->n_vectors;
}

static uword
upf_flowtable_ip4_input (vlib_main_t *vm, vlib_node_runtime_t *node,
                             vlib_frame_t *from_frame)
{
  return upf_flowtable_input (vm, node, from_frame, /* is_ip4 */ 1);
}

static uword
upf_flowtable_ip6_input (vlib_main_t *vm, vlib_node_runtime_t *node,
                             vlib_frame_t *from_frame)
{
  return upf_flowtable_input (vm, node, from_frame, /* is_ip4 */ 0);
}

static char *g_flowtable_error_strings[] = {
#define _(sym, string) string,
    foreach_flowtable_error
#undef _
};

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (flowtable_ip4_input_node) = {
    .function = upf_flowtable_ip4_input,
    .name = "flowtable-ip4-input",
    .vector_size = sizeof (u32),
    .format_trace = format_flowtable_input,
    .type = VLIB_NODE_TYPE_INTERNAL,
    .n_errors = FLOWTABLE_N_ERROR,
    .error_strings = g_flowtable_error_strings,
    .n_next_nodes = FT_NEXT_N_NEXT,
    .next_nodes = {
        [FT_NEXT_DROP] = "error-drop",
        [FT_NEXT_IP_LOOKUP] = "ip6-lookup",
        [FT_NEXT_VXLAN4_ENCAP] = "vxlan4-encap",
        [FT_NEXT_VXLAN6_ENCAP] = "vxlan6-encap",
        [FT_NEXT_FLOWTABLE_ETH] = "flowtable-eth-input",
        [FT_NEXT_IP4_INPUT] = "upf-ip4-input",
        [FT_NEXT_IP6_INPUT] = "upf-ip6-input",
        [FT_NEXT_ERROR_IND] = "upf-error-ind-req",
        [FT_NEXT_L2_INPUT] = "l2-input",
    }};
/* *INDENT-ON* */

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (flowtable_ip6_input_node) = {
    .function = upf_flowtable_ip6_input,
    .name = "flowtable-ip6-input",
    .vector_size = sizeof (u32),
    .format_trace = format_flowtable_input,
    .type = VLIB_NODE_TYPE_INTERNAL,
    .n_errors = FLOWTABLE_N_ERROR,
    .error_strings = g_flowtable_error_strings,
    .n_next_nodes = FT_NEXT_N_NEXT,
    .next_nodes = {
        [FT_NEXT_DROP] = "error-drop",
        [FT_NEXT_IP_LOOKUP] = "ip4-lookup",
        [FT_NEXT_VXLAN4_ENCAP] = "vxlan4-encap",
        [FT_NEXT_VXLAN6_ENCAP] = "vxlan6-encap",
        [FT_NEXT_FLOWTABLE_ETH] = "flowtable-eth-input",
        [FT_NEXT_IP4_INPUT] = "upf-ip4-input",
        [FT_NEXT_IP6_INPUT] = "upf-ip6-input",
        [FT_NEXT_ERROR_IND] = "upf-error-ind-req",
        [FT_NEXT_L2_INPUT] = "l2-input",
    }};
/* *INDENT-ON* */

/* *INDENT-OFF* */
VNET_FEATURE_INIT (flowtable_ip4) = {
    .arc_name = "ip4-unicast",
    .node_name = "flowtable-ip4-input",
    .runs_after = VNET_FEATURES ("ip4-full-reassembly-feature", "nat44-out2in",
                                 "nat44-out2in-worker-handoff"),
    .runs_before = VNET_FEATURES ("ip4-lookup"),
};

VNET_FEATURE_INIT (flowtable_ip6) = {
    .arc_name = "ip6-unicast",
    .node_name = "flowtable-ip6-input",
    .runs_before = VNET_FEATURES ("ip6-lookup"),
    .runs_after = VNET_FEATURES ("ip6-full-reassembly-feature"),
};
/* *INDENT-on* */

/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
