/*
 * Test cases for the improved gtp_parse function
 * This file contains unit tests to verify the GTPU extension header parsing fixes
 */

#include <vlib/vlib.h>
#include <vnet/vnet.h>
#include <upf/upf.h>
#include <upf/flowtable.h>

/* Test data structures */
typedef struct {
    gtpu_header_t gtpu_hdr;
    u8 extension_data[64];
} test_gtpu_packet_t;

/* Test case 1: GTPU packet with no extension headers (next_ext_type = 0x00) */
static void
test_gtpu_no_extension_headers(void)
{
    test_gtpu_packet_t test_pkt = {0};
    tunnel_info_t tunnel = {0};
    gtpu_flags_t flags = {0};
    u32 result;
    
    /* Setup GTPU header with E bit set but no extension headers */
    test_pkt.gtpu_hdr.ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
    test_pkt.gtpu_hdr.type = GTPU_TYPE_GTPU;
    test_pkt.gtpu_hdr.teid = clib_host_to_net_u32(0x12345678);
    test_pkt.gtpu_hdr.next_ext_type = GTP_EX_TYPE_NO_MORE_EX_HDR; /* 0x00 */
    
    result = gtp_parse(&test_pkt, &tunnel, &flags);
    
    /* This should now succeed (return 0) instead of failing */
    if (result == 0 && !flags.format_illegal)
    {
        clib_warning("TEST PASS: GTPU with no extension headers handled correctly");
    }
    else
    {
        clib_warning("TEST FAIL: GTPU with no extension headers should not be marked as illegal");
    }
}

/* Test case 2: GTPU packet with PDU Session Container (next_ext_type = 0x85) */
static void
test_gtpu_pdu_session_container(void)
{
    test_gtpu_packet_t test_pkt = {0};
    tunnel_info_t tunnel = {0};
    gtpu_flags_t flags = {0};
    u32 result;
    pdu_sess_container_ex_t *container;
    
    /* Setup GTPU header with PDU Session Container */
    test_pkt.gtpu_hdr.ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
    test_pkt.gtpu_hdr.type = GTPU_TYPE_GTPU;
    test_pkt.gtpu_hdr.teid = clib_host_to_net_u32(0x12345678);
    test_pkt.gtpu_hdr.next_ext_type = GTP_EX_TYPE_PDU_SESS; /* 0x85 */
    
    /* Setup PDU Session Container */
    container = (pdu_sess_container_ex_t *)test_pkt.extension_data;
    container->ext_hdr_len = 1; /* Minimum valid length */
    container->flags.pdu_type = UPF_DL;
    container->flags.dl_flag.qfi = 5;
    container->flags.dl_flag.qmp = 0;
    container->data[0] = 0; /* Next extension header type = 0 (no more) */
    
    result = gtp_parse(&test_pkt, &tunnel, &flags);
    
    if (result == 0 && !flags.format_illegal && flags.pdu_sess_container)
    {
        clib_warning("TEST PASS: GTPU with PDU Session Container handled correctly");
    }
    else
    {
        clib_warning("TEST FAIL: GTPU with PDU Session Container should be processed correctly");
    }
}

/* Test case 3: GTPU packet with invalid PDU Session Container (length = 0) */
static void
test_gtpu_invalid_pdu_session_container(void)
{
    test_gtpu_packet_t test_pkt = {0};
    tunnel_info_t tunnel = {0};
    gtpu_flags_t flags = {0};
    u32 result;
    pdu_sess_container_ex_t *container;
    
    /* Setup GTPU header with invalid PDU Session Container */
    test_pkt.gtpu_hdr.ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
    test_pkt.gtpu_hdr.type = GTPU_TYPE_GTPU;
    test_pkt.gtpu_hdr.teid = clib_host_to_net_u32(0x12345678);
    test_pkt.gtpu_hdr.next_ext_type = GTP_EX_TYPE_PDU_SESS; /* 0x85 */
    
    /* Setup invalid PDU Session Container with length = 0 */
    container = (pdu_sess_container_ex_t *)test_pkt.extension_data;
    container->ext_hdr_len = 0; /* Invalid length */
    
    result = gtp_parse(&test_pkt, &tunnel, &flags);
    
    if (result == 0 && flags.format_illegal)
    {
        clib_warning("TEST PASS: Invalid PDU Session Container correctly marked as illegal");
    }
    else
    {
        clib_warning("TEST FAIL: Invalid PDU Session Container should be marked as illegal");
    }
}

/* Test case 4: GTPU packet with unknown extension header type */
static void
test_gtpu_unknown_extension_type(void)
{
    test_gtpu_packet_t test_pkt = {0};
    tunnel_info_t tunnel = {0};
    gtpu_flags_t flags = {0};
    u32 result;
    
    /* Setup GTPU header with unknown extension type */
    test_pkt.gtpu_hdr.ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
    test_pkt.gtpu_hdr.type = GTPU_TYPE_GTPU;
    test_pkt.gtpu_hdr.teid = clib_host_to_net_u32(0x12345678);
    test_pkt.gtpu_hdr.next_ext_type = 0xFF; /* Unknown extension type */
    
    result = gtp_parse(&test_pkt, &tunnel, &flags);
    
    /* Should not mark as illegal, just skip processing */
    if (result == 0 && !flags.format_illegal)
    {
        clib_warning("TEST PASS: Unknown extension type handled gracefully");
    }
    else
    {
        clib_warning("TEST FAIL: Unknown extension type should not be marked as illegal");
    }
}

/* Main test function */
void
run_gtp_parse_tests(void)
{
    clib_warning("Starting GTPU parsing tests...");
    
    test_gtpu_no_extension_headers();
    test_gtpu_pdu_session_container();
    test_gtpu_invalid_pdu_session_container();
    test_gtpu_unknown_extension_type();
    
    clib_warning("GTPU parsing tests completed.");
}

/* CLI command to run tests */
static clib_error_t *
test_gtp_parse_command_fn (vlib_main_t * vm,
                          unformat_input_t * input,
                          vlib_cli_command_t * cmd)
{
    run_gtp_parse_tests();
    return 0;
}

VLIB_CLI_COMMAND (test_gtp_parse_command, static) = {
    .path = "test gtp-parse",
    .short_help = "test gtp-parse - Run GTPU parsing unit tests",
    .function = test_gtp_parse_command_fn,
};
