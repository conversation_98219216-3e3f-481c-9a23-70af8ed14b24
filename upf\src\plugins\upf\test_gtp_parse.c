/*
 * Test cases for 16-byte GTPU header handling
 * This file contains unit tests to verify the fix for base station 16-byte GTPU headers
 */

#include <vlib/vlib.h>
#include <vnet/vnet.h>
#include <upf/upf.h>
#include <upf/flowtable.h>

/* Test data structures for 16-byte GTPU header */
typedef struct {
    gtpu_header_t gtpu_hdr;    /* Standard 12-byte header */
    u8 extra_bytes[4];         /* Additional 4 bytes from base station */
    u8 payload_data[64];       /* Actual payload */
} test_16byte_gtpu_packet_t;

/* Test case 1: 基站16字节GTPU头测试 (next_ext_type = 0x00) */
static void
test_16byte_gtpu_header_0x00(void)
{
    test_16byte_gtpu_packet_t test_pkt = {0};
    tunnel_info_t tunnel = {0};
    gtpu_flags_t flags = {0};
    u32 result;

    /* 模拟基站发送的16字节GTPU头 */
    test_pkt.gtpu_hdr.ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
    test_pkt.gtpu_hdr.type = GTPU_TYPE_GTPU;
    test_pkt.gtpu_hdr.teid = clib_host_to_net_u32(0x12345678);
    test_pkt.gtpu_hdr.next_ext_type = 0x00; /* 基站常见的扩展头类型 */

    /* 额外的4字节数据（基站特有） */
    test_pkt.extra_bytes[0] = 0x00;
    test_pkt.extra_bytes[1] = 0x00;
    test_pkt.extra_bytes[2] = 0x00;
    test_pkt.extra_bytes[3] = 0x00;

    result = gtp_parse(&test_pkt, &tunnel, &flags);

    /* 验证：应该成功处理，tunnel.len应该为16，不应该有format_illegal标记 */
    if (result == 0 && !flags.format_illegal && tunnel.len == 16)
    {
        clib_warning("TEST PASS: 16-byte GTPU header (0x00) handled correctly, tunnel.len=%u", tunnel.len);
    }
    else
    {
        clib_warning("TEST FAIL: 16-byte GTPU header (0x00) not handled correctly, result=%u, illegal=%u, len=%u",
                    result, flags.format_illegal, tunnel.len);
    }
}

/* Test case 2: 基站16字节GTPU头测试 (next_ext_type = 0x85) */
static void
test_16byte_gtpu_header_0x85(void)
{
    test_16byte_gtpu_packet_t test_pkt = {0};
    tunnel_info_t tunnel = {0};
    gtpu_flags_t flags = {0};
    u32 result;

    /* 模拟基站发送的16字节GTPU头，扩展头类型为0x85 */
    test_pkt.gtpu_hdr.ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
    test_pkt.gtpu_hdr.type = GTPU_TYPE_GTPU;
    test_pkt.gtpu_hdr.teid = clib_host_to_net_u32(0x87654321);
    test_pkt.gtpu_hdr.next_ext_type = 0x85; /* PDU Session Container类型 */

    /* 额外的4字节数据（基站特有） */
    test_pkt.extra_bytes[0] = 0x01;  /* 可能的扩展头长度 */
    test_pkt.extra_bytes[1] = 0x00;
    test_pkt.extra_bytes[2] = 0x00;
    test_pkt.extra_bytes[3] = 0x00;

    result = gtp_parse(&test_pkt, &tunnel, &flags);

    /* 验证：应该成功处理，tunnel.len应该为16，不应该有format_illegal标记 */
    if (result == 0 && !flags.format_illegal && tunnel.len == 16)
    {
        clib_warning("TEST PASS: 16-byte GTPU header (0x85) handled correctly, tunnel.len=%u", tunnel.len);
    }
    else
    {
        clib_warning("TEST FAIL: 16-byte GTPU header (0x85) not handled correctly, result=%u, illegal=%u, len=%u",
                    result, flags.format_illegal, tunnel.len);
    }
}

/* Test case 3: 验证其他扩展头类型也使用16字节长度 */
static void
test_16byte_gtpu_header_other_types(void)
{
    test_16byte_gtpu_packet_t test_pkt = {0};
    tunnel_info_t tunnel = {0};
    gtpu_flags_t flags = {0};
    u32 result;

    /* 测试其他扩展头类型（如0xFF） */
    test_pkt.gtpu_hdr.ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
    test_pkt.gtpu_hdr.type = GTPU_TYPE_GTPU;
    test_pkt.gtpu_hdr.teid = clib_host_to_net_u32(0xABCDEF00);
    test_pkt.gtpu_hdr.next_ext_type = 0xFF; /* 其他扩展头类型 */

    /* 额外的4字节数据 */
    test_pkt.extra_bytes[0] = 0xFF;
    test_pkt.extra_bytes[1] = 0xFF;
    test_pkt.extra_bytes[2] = 0xFF;
    test_pkt.extra_bytes[3] = 0xFF;

    result = gtp_parse(&test_pkt, &tunnel, &flags);

    /* 验证：应该成功处理，tunnel.len应该为16 */
    if (result == 0 && !flags.format_illegal && tunnel.len == 16)
    {
        clib_warning("TEST PASS: 16-byte GTPU header (0xFF) handled correctly, tunnel.len=%u", tunnel.len);
    }
    else
    {
        clib_warning("TEST FAIL: 16-byte GTPU header (0xFF) not handled correctly, result=%u, illegal=%u, len=%u",
                    result, flags.format_illegal, tunnel.len);
    }
}

/* Main test function for 16-byte GTPU header */
void
run_16byte_gtpu_tests(void)
{
    clib_warning("Starting 16-byte GTPU header tests...");

    test_16byte_gtpu_header_0x00();
    test_16byte_gtpu_header_0x85();
    test_16byte_gtpu_header_other_types();

    clib_warning("16-byte GTPU header tests completed.");
}

/* CLI command to run 16-byte GTPU header tests */
static clib_error_t *
test_16byte_gtpu_command_fn (vlib_main_t * vm,
                            unformat_input_t * input,
                            vlib_cli_command_t * cmd)
{
    run_16byte_gtpu_tests();
    return 0;
}

VLIB_CLI_COMMAND (test_16byte_gtpu_command, static) = {
    .path = "test 16byte-gtpu",
    .short_help = "test 16byte-gtpu - Run 16-byte GTPU header tests",
    .function = test_16byte_gtpu_command_fn,
};
